"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2877],{94630:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(39763).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},32449:function(e,t,r){r.d(t,{VY:function(){return J},aV:function(){return H},fC:function(){return Z},xz:function(){return Y}});var n=r(2265),o=r(6741),a=r(73966),i=r(98575),l=r(37053),u=r(57437),c=r(99255),s=r(66840),f=r(26606),d=r(80886),v=n.createContext(void 0);function p(e){let t=n.useContext(v);return e||t||"ltr"}var m="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[h,y,g]=function(e){let t=e+"CollectionProvider",[r,o]=(0,a.b)(t),[c,s]=r(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:r}=e,o=n.useRef(null),a=n.useRef(new Map).current;return(0,u.jsx)(c,{scope:t,itemMap:a,collectionRef:o,children:r})};f.displayName=t;let d=e+"CollectionSlot",v=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=s(d,r),a=(0,i.e)(t,o.collectionRef);return(0,u.jsx)(l.g7,{ref:a,children:n})});v.displayName=d;let p=e+"CollectionItemSlot",m="data-radix-collection-item",b=n.forwardRef((e,t)=>{let{scope:r,children:o,...a}=e,c=n.useRef(null),f=(0,i.e)(t,c),d=s(p,r);return n.useEffect(()=>(d.itemMap.set(c,{ref:c,...a}),()=>void d.itemMap.delete(c))),(0,u.jsx)(l.g7,{[m]:"",ref:f,children:o})});return b.displayName=p,[{Provider:f,Slot:v,ItemSlot:b},function(t){let r=s(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},o]}(w),[x,R]=(0,a.b)(w,[g]),[M,C]=x(w),I=n.forwardRef((e,t)=>(0,u.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(j,{...e,ref:t})})}));I.displayName=w;var j=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:l=!1,dir:c,currentTabStopId:v,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:h,onEntryFocus:g,preventScrollOnEntryFocus:x=!1,...R}=e,C=n.useRef(null),I=(0,i.e)(t,C),j=p(c),[F=null,A]=(0,d.T)({prop:v,defaultProp:w,onChange:h}),[D,E]=n.useState(!1),k=(0,f.W)(g),S=y(r),V=n.useRef(!1),[N,K]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(m,k),()=>e.removeEventListener(m,k)},[k]),(0,u.jsx)(M,{scope:r,orientation:a,dir:j,loop:l,currentTabStopId:F,onItemFocus:n.useCallback(e=>A(e),[A]),onItemShiftTab:n.useCallback(()=>E(!0),[]),onFocusableItemAdd:n.useCallback(()=>K(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>K(e=>e-1),[]),children:(0,u.jsx)(s.WV.div,{tabIndex:D||0===N?-1:0,"data-orientation":a,...R,ref:I,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{V.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!V.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(m,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=S().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===F),...e].filter(Boolean).map(e=>e.ref.current),x)}}V.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>E(!1))})})}),F="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:l,...f}=e,d=(0,c.M)(),v=l||d,p=C(F,r),m=p.currentTabStopId===v,b=y(r),{onFocusableItemAdd:w,onFocusableItemRemove:g}=p;return n.useEffect(()=>{if(a)return w(),()=>g()},[a,w,g]),(0,u.jsx)(h.ItemSlot,{scope:r,id:v,focusable:a,active:i,children:(0,u.jsx)(s.WV.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...f,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?p.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>p.onItemFocus(v)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return D[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var r,n;"prev"===t&&o.reverse();let a=o.indexOf(e.currentTarget);o=p.loop?(r=o,n=a+1,r.map((e,t)=>r[(n+t)%r.length])):o.slice(a+1)}setTimeout(()=>T(o))}})})})});A.displayName=F;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var E=r(71599),k="Tabs",[S,V]=(0,a.b)(k,[R]),N=R(),[K,P]=S(k),L=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:f="automatic",...v}=e,m=p(l),[b,w]=(0,d.T)({prop:n,onChange:o,defaultProp:a});return(0,u.jsx)(K,{scope:r,baseId:(0,c.M)(),value:b,onValueChange:w,orientation:i,dir:m,activationMode:f,children:(0,u.jsx)(s.WV.div,{dir:m,"data-orientation":i,...v,ref:t})})});L.displayName=k;var _="TabsList",W=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,a=P(_,r),i=N(r);return(0,u.jsx)(I,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:n,children:(0,u.jsx)(s.WV.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});W.displayName=_;var G="TabsTrigger",q=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...i}=e,l=P(G,r),c=N(r),f=O(l.baseId,n),d=U(l.baseId,n),v=n===l.value;return(0,u.jsx)(A,{asChild:!0,...c,focusable:!a,active:v,children:(0,u.jsx)(s.WV.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":d,"data-state":v?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:f,...i,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==l.activationMode;v||a||!e||l.onValueChange(n)})})})});q.displayName=G;var z="TabsContent",B=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:a,children:i,...l}=e,c=P(z,r),f=O(c.baseId,o),d=U(c.baseId,o),v=o===c.value,p=n.useRef(v);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(E.z,{present:a||v,children:r=>{let{present:n}=r;return(0,u.jsx)(s.WV.div,{"data-state":v?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":f,hidden:!n,id:d,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:n&&i})}})});function O(e,t){return"".concat(e,"-trigger-").concat(t)}function U(e,t){return"".concat(e,"-content-").concat(t)}B.displayName=z;var Z=L,H=W,Y=q,J=B}}]);