"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_lib_localCartStore_ts-39840d39"],{

/***/ "(app-pages-browser)/./src/lib/localCartStore.ts":
/*!***********************************!*\
  !*** ./src/lib/localCartStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCartAfterCheckout: function() { return /* binding */ clearCartAfterCheckout; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   useLocalCartCount: function() { return /* binding */ useLocalCartCount; },\n/* harmony export */   useLocalCartError: function() { return /* binding */ useLocalCartError; },\n/* harmony export */   useLocalCartItems: function() { return /* binding */ useLocalCartItems; },\n/* harmony export */   useLocalCartLoading: function() { return /* binding */ useLocalCartLoading; },\n/* harmony export */   useLocalCartStore: function() { return /* binding */ useLocalCartStore; },\n/* harmony export */   useLocalCartSubtotal: function() { return /* binding */ useLocalCartSubtotal; },\n/* harmony export */   useLocalCartTotal: function() { return /* binding */ useLocalCartTotal; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/**\n * Local Cart Store for Ankkor E-commerce\n *\n * This implementation uses local storage to persist cart data on the client side.\n * When the user proceeds to checkout, the cart items are sent to WooCommerce\n * using the Store API to create a server-side cart before redirecting to the checkout page.\n */ /* __next_internal_client_entry_do_not_use__ useLocalCartStore,useLocalCartItems,useLocalCartCount,useLocalCartSubtotal,useLocalCartTotal,useLocalCartLoading,useLocalCartError,formatPrice,clearCartAfterCheckout auto */ \n\n// Local storage version to handle migrations\nconst STORAGE_VERSION = 1;\n// Generate a unique ID for cart items\nconst generateItemId = ()=>{\n    return Math.random().toString(36).substring(2, 15);\n};\n// Validate product stock before adding to cart\nconst validateProductStock = async (productId, requestedQuantity, variationId)=>{\n    try {\n        // Check real-time stock from your API\n        const response = await fetch(\"/api/products/\".concat(productId, \"/stock\").concat(variationId ? \"?variation_id=\".concat(variationId) : \"\"));\n        if (!response.ok) {\n            return {\n                available: false,\n                message: \"Unable to verify stock availability\"\n            };\n        }\n        const stockData = await response.json();\n        // Check if product is in stock\n        if (stockData.stockStatus !== \"IN_STOCK\" && stockData.stockStatus !== \"instock\") {\n            return {\n                available: false,\n                message: \"This product is currently out of stock\",\n                stockStatus: stockData.stockStatus\n            };\n        }\n        // Check if requested quantity is available\n        if (stockData.stockQuantity !== null && stockData.stockQuantity < requestedQuantity) {\n            return {\n                available: false,\n                message: \"Only \".concat(stockData.stockQuantity, \" items available in stock\"),\n                stockQuantity: stockData.stockQuantity,\n                stockStatus: stockData.stockStatus\n            };\n        }\n        return {\n            available: true,\n            stockQuantity: stockData.stockQuantity,\n            stockStatus: stockData.stockStatus\n        };\n    } catch (error) {\n        console.error(\"Stock validation error:\", error);\n        // In case of error, allow the add to cart but log the issue\n        return {\n            available: true,\n            message: \"Stock validation temporarily unavailable\"\n        };\n    }\n};\n// Create the store\nconst useLocalCartStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // State\n        items: [],\n        itemCount: 0,\n        isLoading: false,\n        error: null,\n        // Actions\n        addToCart: async (item)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                // Validate stock before adding to cart\n                const stockValidation = await validateProductStock(item.productId, item.quantity, item.variationId);\n                if (!stockValidation.available) {\n                    throw new Error(stockValidation.message || \"Product is out of stock\");\n                }\n                const items = get().items;\n                // Normalize price format - remove currency symbols and commas\n                let normalizedPrice = item.price;\n                if (typeof normalizedPrice === \"string\") {\n                    // Remove currency symbol if present\n                    const priceString = normalizedPrice.replace(/[₹$€£]/g, \"\").trim();\n                    // Replace comma with empty string if present (for Indian number format)\n                    normalizedPrice = priceString.replace(/,/g, \"\");\n                }\n                // Create a normalized item with clean price\n                const normalizedItem = {\n                    ...item,\n                    price: normalizedPrice\n                };\n                // Check if the item already exists in the cart\n                const existingItemIndex = items.findIndex((cartItem)=>cartItem.productId === normalizedItem.productId && cartItem.variationId === normalizedItem.variationId);\n                if (existingItemIndex !== -1) {\n                    // If item exists, update quantity\n                    const updatedItems = [\n                        ...items\n                    ];\n                    updatedItems[existingItemIndex].quantity += normalizedItem.quantity;\n                    set({\n                        items: updatedItems,\n                        itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                        isLoading: false\n                    });\n                } else {\n                    // If item doesn't exist, add it with a new ID\n                    const newItem = {\n                        ...normalizedItem,\n                        id: generateItemId()\n                    };\n                    set({\n                        items: [\n                            ...items,\n                            newItem\n                        ],\n                        itemCount: items.reduce((sum, item)=>sum + item.quantity, 0) + newItem.quantity,\n                        isLoading: false\n                    });\n                }\n                // Show success message\n                console.log(\"Item added to cart successfully\");\n                // Store the updated cart in localStorage immediately to prevent loss\n                if (true) {\n                    try {\n                        const state = {\n                            state: {\n                                items: get().items,\n                                itemCount: get().itemCount,\n                                isLoading: false,\n                                error: null\n                            },\n                            version: STORAGE_VERSION\n                        };\n                        localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                    } catch (storageError) {\n                        console.warn(\"Failed to manually persist cart to localStorage:\", storageError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error adding item to cart:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        updateCartItem: (id, quantity)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                if (quantity <= 0) {\n                    // If quantity is 0 or negative, remove the item\n                    return get().removeCartItem(id);\n                }\n                // Find the item and update its quantity\n                const updatedItems = items.map((item)=>item.id === id ? {\n                        ...item,\n                        quantity\n                    } : item);\n                set({\n                    items: updatedItems,\n                    itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                    isLoading: false\n                });\n                // Immediately persist to localStorage\n                if (true) {\n                    try {\n                        const state = {\n                            state: {\n                                items: updatedItems,\n                                itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                                isLoading: false,\n                                error: null\n                            },\n                            version: STORAGE_VERSION\n                        };\n                        localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                    } catch (storageError) {\n                        console.warn(\"Failed to manually persist cart update to localStorage:\", storageError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error updating cart item:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        removeCartItem: (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                const updatedItems = items.filter((item)=>item.id !== id);\n                set({\n                    items: updatedItems,\n                    itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                    isLoading: false\n                });\n                // Immediately persist to localStorage\n                if (true) {\n                    try {\n                        const state = {\n                            state: {\n                                items: updatedItems,\n                                itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                                isLoading: false,\n                                error: null\n                            },\n                            version: STORAGE_VERSION\n                        };\n                        localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                    } catch (storageError) {\n                        console.warn(\"Failed to manually persist cart removal to localStorage:\", storageError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error removing cart item:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        clearCart: ()=>{\n            set({\n                items: [],\n                itemCount: 0,\n                isLoading: false,\n                error: null\n            });\n            // Immediately persist to localStorage\n            if (true) {\n                try {\n                    const state = {\n                        state: {\n                            items: [],\n                            itemCount: 0,\n                            isLoading: false,\n                            error: null\n                        },\n                        version: STORAGE_VERSION\n                    };\n                    localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                } catch (storageError) {\n                    console.warn(\"Failed to manually persist cart clearing to localStorage:\", storageError);\n                }\n            }\n        },\n        setError: (error)=>{\n            set({\n                error\n            });\n        },\n        setIsLoading: (isLoading)=>{\n            set({\n                isLoading\n            });\n        },\n        // Helper methods\n        subtotal: ()=>{\n            const items = get().items;\n            try {\n                const calculatedSubtotal = items.reduce((total, item)=>{\n                    // Handle price with or without currency symbol\n                    let itemPrice = 0;\n                    if (typeof item.price === \"string\") {\n                        // Remove currency symbol if present\n                        const priceString = item.price.replace(/[₹$€£]/g, \"\").trim();\n                        // Replace comma with empty string if present (for Indian number format)\n                        const cleanPrice = priceString.replace(/,/g, \"\");\n                        itemPrice = parseFloat(cleanPrice);\n                    } else {\n                        itemPrice = item.price;\n                    }\n                    if (isNaN(itemPrice)) {\n                        console.warn(\"Invalid price for item \".concat(item.id, \": \").concat(item.price));\n                        return total;\n                    }\n                    return total + itemPrice * item.quantity;\n                }, 0);\n                return isNaN(calculatedSubtotal) ? 0 : calculatedSubtotal;\n            } catch (error) {\n                console.error(\"Error calculating subtotal:\", error);\n                return 0;\n            }\n        },\n        total: ()=>{\n            // For now, total is the same as subtotal\n            // In the future, you could add shipping, tax, etc.\n            const calculatedTotal = get().subtotal();\n            return isNaN(calculatedTotal) ? 0 : calculatedTotal;\n        },\n        // Sync cart with WooCommerce using Store API\n        syncWithWooCommerce: async (authToken)=>{\n            const { items } = get();\n            if (items.length === 0) {\n                throw new Error(\"Cart is empty\");\n            }\n            try {\n                console.log(\"Syncing cart with WooCommerce...\");\n                console.log(\"Auth token provided:\", !!authToken);\n                set({\n                    isLoading: true\n                });\n                // If user is logged in, use the JWT-to-Cookie bridge for seamless checkout\n                if (authToken) {\n                    console.log(\"User is authenticated, using JWT-to-Cookie bridge\");\n                    try {\n                        const checkoutUrl = await createWpSessionAndGetCheckoutUrl(authToken, items);\n                        set({\n                            isLoading: false\n                        });\n                        return checkoutUrl;\n                    } catch (bridgeError) {\n                        console.error(\"JWT-to-Cookie bridge failed:\", bridgeError);\n                        // Fall back to guest checkout if the bridge fails\n                        console.log(\"Falling back to guest checkout...\");\n                    // Continue with guest checkout flow below\n                    }\n                }\n                // For guest users, redirect directly to WooCommerce checkout\n                console.log(\"User is not authenticated, redirecting to WooCommerce checkout\");\n                const baseUrl = \"https://maroon-lapwing-781450.hostingersite.com\" || 0;\n                const checkoutUrl = \"\".concat(baseUrl, \"/checkout/\");\n                console.log(\"Guest checkout URL:\", checkoutUrl);\n                set({\n                    isLoading: false\n                });\n                return checkoutUrl;\n            } catch (error) {\n                console.error(\"Error syncing cart with WooCommerce:\", error);\n                set({\n                    isLoading: false\n                });\n                // Fallback approach: use URL parameters to build cart\n                try {\n                    console.log(\"Attempting fallback method for cart sync...\");\n                    const baseUrl = \"https://maroon-lapwing-781450.hostingersite.com\" || 0;\n                    // Build URL with add-to-cart parameters for each item\n                    let checkoutUrl = \"\".concat(baseUrl, \"/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1\");\n                    // Add each item as a URL parameter\n                    items.forEach((item, index)=>{\n                        if (index === 0) {\n                            checkoutUrl += \"&add-to-cart=\".concat(item.productId, \"&quantity=\").concat(item.quantity);\n                        } else {\n                            // For WooCommerce, additional items need a different format\n                            checkoutUrl += \"&add-to-cart[\".concat(index, \"]=\").concat(item.productId, \"&quantity[\").concat(index, \"]=\").concat(item.quantity);\n                        }\n                        // Add variation ID if present\n                        if (item.variationId) {\n                            checkoutUrl += \"&variation_id=\".concat(item.variationId);\n                        }\n                    });\n                    console.log(\"Fallback checkout URL:\", checkoutUrl);\n                    return checkoutUrl;\n                } catch (fallbackError) {\n                    console.error(\"Fallback method failed:\", fallbackError);\n                    throw new Error(\"Failed to sync cart with WooCommerce. Please try again or contact support.\");\n                }\n            }\n        }\n    }), {\n    name: \"ankkor-local-cart\",\n    version: STORAGE_VERSION\n}));\n// Helper hooks\nconst useLocalCartItems = ()=>useLocalCartStore((state)=>state.items);\nconst useLocalCartCount = ()=>useLocalCartStore((state)=>state.itemCount);\nconst useLocalCartSubtotal = ()=>useLocalCartStore((state)=>state.subtotal());\nconst useLocalCartTotal = ()=>useLocalCartStore((state)=>state.total());\nconst useLocalCartLoading = ()=>useLocalCartStore((state)=>state.isLoading);\nconst useLocalCartError = ()=>useLocalCartStore((state)=>state.error);\n// Helper functions\nconst formatPrice = function(price) {\n    let currencyCode = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"INR\";\n    const amount = typeof price === \"string\" ? parseFloat(price) : price;\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency: currencyCode,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(amount);\n};\n// Clear cart after successful checkout\nconst clearCartAfterCheckout = ()=>{\n    useLocalCartStore.getState().clearCart();\n// Also reset the cart token to ensure a fresh cart for the next session\n// cartSession.resetCartToken(); // This line was removed as per the edit hint\n};\n/**\n * Create WordPress session from JWT token and get the checkout URL\n * This implements the JWT-to-Cookie Bridge for seamless checkout experience\n * @param authToken The JWT authentication token\n * @param items Cart items to include in checkout\n * @returns The WooCommerce checkout URL\n */ async function createWpSessionAndGetCheckoutUrl(authToken, items) {\n    if (!authToken) {\n        throw new Error(\"Authentication token is required\");\n    }\n    const wpUrl = \"https://maroon-lapwing-781450.hostingersite.com\";\n    const checkoutUrl = \"https://maroon-lapwing-781450.hostingersite.com/checkout/\";\n    if (!wpUrl || !checkoutUrl) {\n        throw new Error(\"WordPress or checkout URL not configured. Check your environment variables.\");\n    }\n    try {\n        console.log(\"Creating WordPress session from JWT token...\");\n        console.log(\"Using endpoint:\", \"\".concat(wpUrl, \"/wp-json/headless/v1/create-wp-session\"));\n        console.log(\"Token length:\", authToken.length);\n        console.log(\"Token preview:\", authToken.substring(0, 20) + \"...\");\n        // Call the custom WordPress endpoint to create a session from JWT\n        const response = await fetch(\"\".concat(wpUrl, \"/wp-json/headless/v1/create-wp-session\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(authToken)\n            },\n            // THIS IS THE CRITICAL LINE - Include token in request body as well\n            body: JSON.stringify({\n                token: authToken\n            }),\n            credentials: \"include\"\n        });\n        console.log(\"Response status:\", response.status);\n        console.log(\"Response headers:\", Object.fromEntries(response.headers.entries()));\n        if (!response.ok) {\n            let errorMessage = \"HTTP \".concat(response.status, \": \").concat(response.statusText);\n            try {\n                const errorData = await response.json();\n                errorMessage = errorData.message || errorData.code || errorMessage;\n                console.error(\"Error response data:\", errorData);\n            } catch (parseError) {\n                console.error(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(\"Failed to create WordPress session: \".concat(errorMessage));\n        }\n        const data = await response.json();\n        console.log(\"Response data:\", data);\n        if (!data.success) {\n            throw new Error(data.message || \"Failed to create WordPress session\");\n        }\n        console.log(\"WordPress session created successfully\");\n        console.log(\"Redirecting to checkout URL:\", checkoutUrl);\n        // For authenticated users, we can directly go to checkout\n        // The server already has the user's session and will load the correct cart\n        return checkoutUrl;\n    } catch (error) {\n        console.error(\"Error creating WordPress session:\", error);\n        // Provide more specific error messages\n        if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n            throw new Error(\"Network error: Could not connect to WordPress. Please check your internet connection.\");\n        }\n        throw new Error(error instanceof Error ? error.message : \"Failed to prepare checkout\");\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/localCartStore.ts\n"));

/***/ })

}]);