(()=>{var e={};e.id=1599,e.ids=[1599],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},37523:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{GlobalError:()=>c.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>f,routeModule:()=>g,tree:()=>p});var s=r(8179);r(52617),r(12523);var a=r(23191),o=r(88716),i=r(37922),c=r.n(i),l=r(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);var u=e([s]);s=(u.then?(await u)():u)[0];let p=["",{children:["product",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8179)),"E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],f=["E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"],m="/product/[slug]/page",x={require:r,loadChunk:()=>Promise.resolve()},g=new a.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/product/[slug]/page",pathname:"/product/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});n()}catch(e){n(e)}})},72996:(e,t,r)=>{Promise.resolve().then(r.bind(r,387))},387:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var n=r(10326),s=r(17577),a=r(46226),o=r(92148),i=r(86806),c=r(72248),l=r(77321),d=r(76557);let u=(0,d.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),p=(0,d.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var f=r(34565);let m=({product:e})=>{let[t,r]=(0,s.useState)(0),[d,m]=(0,s.useState)(1),[x,g]=(0,s.useState)(null),[h,v]=(0,s.useState)({}),[y,b]=(0,s.useState)(!1),j=(0,i.rY)(),{openCart:N}=(0,l.j)(),{id:k,databaseId:w,name:_,description:S,shortDescription:P,price:R,regularPrice:E,onSale:O,stockStatus:T,image:M,galleryImages:A,attributes:C,type:q,variations:F}=e,U="VARIABLE"===q,$=[M?.sourceUrl?{sourceUrl:M.sourceUrl,altText:M.altText||_}:null,...A?.nodes||[]].filter(Boolean),z=(e,t)=>{if(v(r=>({...r,[e]:t})),U&&F?.nodes){let r={...h,[e]:t};if(C?.nodes?.every(e=>r[e.name])){let e=F.nodes.find(e=>e.attributes.nodes.every(e=>{let t=r[e.name];return e.value===t}));e?g(e):g(null)}}},I=async()=>{b(!0);try{let e={productId:w.toString(),quantity:d,name:_,price:x?.price||R,image:{url:$[0]?.sourceUrl||"",altText:$[0]?.altText||_}};await j.addToCart(e),N()}catch(e){console.error("Error adding product to cart:",e)}finally{b(!1)}},L="IN_STOCK"!==T,D=!U||U&&x;return n.jsx("div",{className:"container mx-auto px-4 py-12",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,n.jsxs)("div",{className:"space-y-6",children:[n.jsx("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden",children:$[t]?.sourceUrl&&n.jsx(a.default,{src:$[t].sourceUrl,alt:$[t].altText||_,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,className:"object-cover"})}),$.length>1&&n.jsx("div",{className:"grid grid-cols-5 gap-2",children:$.map((e,s)=>n.jsx("button",{onClick:()=>r(s),className:`relative aspect-square bg-[#f4f3f0] ${t===s?"ring-2 ring-[#2c2c27]":""}`,children:n.jsx(a.default,{src:e.sourceUrl,alt:e.altText||`${_} - Image ${s+1}`,fill:!0,sizes:"(max-width: 768px) 20vw, 10vw",className:"object-cover"})},s))})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[n.jsx("h1",{className:"text-3xl font-serif text-[#2c2c27]",children:_}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx("span",{className:"text-xl font-medium text-[#2c2c27]",children:(x?.price||R).toString().includes("₹")||(x?.price||R).toString().includes("$")||(x?.price||R).toString().includes("€")||(x?.price||R).toString().includes("\xa3")?x?.price||R:`₹${x?.price||R}`}),O&&E&&n.jsx("span",{className:"text-sm line-through text-[#8a8778]",children:E.toString().includes("₹")||E.toString().includes("$")||E.toString().includes("€")||E.toString().includes("\xa3")?E:`₹${E}`})]}),P&&n.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:P}}),U&&C?.nodes&&n.jsx("div",{className:"space-y-4",children:C.nodes.map(e=>(0,n.jsxs)("div",{className:"space-y-2",children:[n.jsx("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),n.jsx("div",{className:"flex flex-wrap gap-2",children:e.options.map(t=>n.jsx("button",{onClick:()=>z(e.name,t),className:`px-4 py-2 border ${h[e.name]===t?"border-[#2c2c27] bg-[#2c2c27] text-white":"border-gray-300 hover:border-[#8a8778]"}`,children:t},t))})]},e.name))}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[n.jsx("span",{className:"text-[#5c5c52]",children:"Quantity:"}),(0,n.jsxs)("div",{className:"flex items-center border border-gray-300",children:[n.jsx("button",{onClick:()=>m(e=>e>1?e-1:1),disabled:d<=1,className:"px-3 py-2 hover:bg-gray-100","aria-label":"Decrease quantity",children:n.jsx(u,{className:"h-4 w-4"})}),n.jsx("span",{className:"px-4 py-2 border-x border-gray-300",children:d}),n.jsx("button",{onClick:()=>m(e=>e+1),className:"px-3 py-2 hover:bg-gray-100","aria-label":"Increase quantity",children:n.jsx(p,{className:"h-4 w-4"})})]})]}),(0,n.jsxs)("div",{className:"text-sm",children:[n.jsx("span",{className:"font-medium",children:"Availability: "}),n.jsx("span",{className:L?"text-red-600":"text-green-600",children:L?"Out of Stock":"In Stock"})]}),(0,n.jsxs)(o.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,n.jsxs)(c.z,{onClick:I,disabled:L||y||!D,className:"w-full py-6 bg-[#2c2c27] text-white hover:bg-[#3c3c37] flex items-center justify-center gap-2",children:[n.jsx(f.Z,{className:"h-5 w-5"}),y?"Adding...":"Add to Cart"]}),U&&!D&&!L&&n.jsx("p",{className:"mt-2 text-sm text-red-600",children:"Please select all options to add this product to your cart"})]}),S&&(0,n.jsxs)("div",{className:"mt-12 border-t border-gray-200 pt-8",children:[n.jsx("h2",{className:"text-xl font-serif mb-4 text-[#2c2c27]",children:"Description"}),n.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:S}})]})]})]})})}},72248:(e,t,r)=>{"use strict";let n,s;r.d(t,{z:()=>p});var a=r(10326);r(17577);var o=r(34214),i=r(41135);let c=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=i.W;var d=r(51223);let u=(n="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s={variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}},e=>{var t;if((null==s?void 0:s.variants)==null)return l(n,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:r,defaultVariants:a}=s,o=Object.keys(r).map(t=>{let n=null==e?void 0:e[t],s=null==a?void 0:a[t];if(null===n)return null;let o=c(n)||c(s);return r[t][o]}),i=e&&Object.entries(e).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return l(n,o,null==s?void 0:null===(t=s.compoundVariants)||void 0===t?void 0:t.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...i}[t]):({...a,...i})[t]===r})?[...e,r,n]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)});function p({className:e,variant:t,size:r,asChild:n=!1,...s}){let i=n?o.g7:"button";return a.jsx(i,{"data-slot":"button",className:(0,d.cn)(u({variant:t,size:r,className:e})),...s})}},58585:(e,t,r)=>{"use strict";var n=r(61085);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return o},RedirectType:function(){return n.RedirectType},notFound:function(){return s.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(83953),s=r(16399);class a extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class o extends URLSearchParams{append(){throw new a}delete(){throw new a}set(){throw new a}sort(){throw new a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return s},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return c},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return u},permanentRedirect:function(){return d},redirect:function(){return l}});let s=r(54580),a=r(72934),o=r(8586),i="NEXT_REDIRECT";function c(e,t,r){void 0===r&&(r=o.RedirectStatusCode.TemporaryRedirect);let n=Error(i);n.digest=i+";"+t+";"+e+";"+r+";";let a=s.requestAsyncStorage.getStore();return a&&(n.mutableCookies=a.mutableCookies),n}function l(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw c(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.TemporaryRedirect)}function d(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw c(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,s]=e.digest.split(";",4),a=Number(s);return t===i&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(a)&&a in o.RedirectStatusCode}function p(e){return u(e)?e.digest.split(";",3)[2]:null}function f(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8179:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>d,generateMetadata:()=>l});var s=r(19510),a=r(58585),o=r(19910),i=r(80151),c=e([o]);async function l({params:e}){let{slug:t}=e;try{let e=await (0,o.gF)(t);if(!e)return{title:"Product Not Found | Ankkor",description:"The requested product could not be found."};return{title:`${e.name} | Ankkor`,description:e.shortDescription||e.description||"Luxury menswear from Ankkor.",openGraph:{images:e.image?[{url:e.image.sourceUrl,alt:e.name}]:[]}}}catch(e){return console.error("Error generating product metadata:",e),{title:"Product | Ankkor",description:"Luxury menswear from Ankkor."}}}async function d({params:e}){let{slug:t}=e;try{let e=await (0,o.gF)(t);return e||(0,a.notFound)(),s.jsx(i.Z,{product:e})}catch(e){console.error("Error fetching product:",e),(0,a.notFound)()}}o=(c.then?(await c)():c)[0],n()}catch(e){n(e)}})},80151:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\product\ProductDetail.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,2738,4766,4868,2993,9910],()=>r(37523));module.exports=n})();