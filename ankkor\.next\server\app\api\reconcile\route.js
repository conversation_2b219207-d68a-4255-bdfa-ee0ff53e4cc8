"use strict";(()=>{var e={};e.id=9417,e.ids=[9417],e.modules={45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},93690:e=>{e.exports=import("graphql-request")},80543:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>u,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>p});var s=r(49303),c=r(88716),n=r(60670),a=r(44343),i=e([a]);a=(i.then?(await i)():i)[0];let l=new s.AppRouteRouteModule({definition:{kind:c.x.APP_ROUTE,page:"/api/reconcile/route",pathname:"/api/reconcile",filename:"route",bundlePath:"app/api/reconcile/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\reconcile\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:p,serverHooks:g}=l,y="/api/reconcile/route";function u(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:p})}o()}catch(e){o(e)}})},44343:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{GET:()=>i,POST:()=>a});var s=r(87070),c=r(62980),n=e([c]);async function a(e){try{let t;let r=e.headers.get("x-api-key"),o=process.env.SHOPIFY_REVALIDATION_SECRET;if(!r||r!==o)return s.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{type:n="inventory",handle:a,force:i=!1}=await e.json().catch(()=>({}));switch(n){case"all-products":t=await (0,c.XD)();break;case"inventory":t=await (0,c.pN)(a);break;case"product":if(!a)return s.NextResponse.json({success:!1,error:'Product handle is required for type "product"'},{status:400});t=await (0,c.R8)(a);break;default:return s.NextResponse.json({success:!1,error:`Unknown reconciliation type: ${n}`},{status:400})}return s.NextResponse.json({success:!0,type:n,handle:a,result:t},{status:200})}catch(e){return console.error("Error in reconciliation API:",e),s.NextResponse.json({success:!1,error:e.message},{status:500})}}async function i(e){return s.NextResponse.json({status:"Reconciliation API is active"},{status:200})}c=(n.then?(await n)():n)[0],o()}catch(e){o(e)}})},62980:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.d(t,{R8:()=>l,XD:()=>u,pN:()=>i});var s=r(19910),c=r(92861),n=r(57708),a=e([s]);async function i(){try{console.log("Starting inventory reconciliation...");let e=await s.Xp(),t=await (0,c.Ls)(),r=0,o=0,a=0;for(let s of e){let e=s.id.toString();t[e]?t[e].inventory!==s.stock_quantity?(t[e].inventory=s.stock_quantity||0,r++):o++:(t[e]={wooId:e,inventory:s.stock_quantity||0,sku:s.sku||"",title:s.name||"",lastUpdated:new Date().toISOString()},a++)}return await (0,c.wm)(t),(0,n.revalidatePath)("/product/[slug]"),(0,n.revalidatePath)("/categories"),console.log(`Reconciliation complete: ${r} updated, ${a} added, ${o} unchanged`),{success:!0,stats:{updated:r,added:a,unchanged:o}}}catch(e){return console.error("Error during inventory reconciliation:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function u(){try{console.log("Starting full product reconciliation...");let e=await s.Dg(100);if(!e||0===e.length)return console.log("No products found to reconcile"),{success:!0,message:"No products found",productsProcessed:0};console.log(`Found ${e.length} products to reconcile`);let t=0,r=[];for(let o of e)try{s.Op(o)&&(await c.ho(o.databaseId.toString(),o.slug),t++)}catch(t){let e=`Failed to process product ${o.slug}: ${t}`;console.error(e),r.push(e)}return console.log(`Successfully reconciled ${t} products`),{success:!0,message:`Reconciled ${t} products`,productsProcessed:t,errors:r.length>0?r:void 0}}catch(e){return console.error("Error in reconcileAllProducts:",e),{success:!1,message:`Failed to reconcile products: ${e}`,productsProcessed:0}}}async function l(e){try{console.log(`Starting reconciliation for product: ${e}`);let t=await s.Co(e);if(!t)return{success:!1,message:`Product not found: ${e}`,productHandle:e};let r=s.Op(t);if(r)return await c.ho(t.databaseId.toString(),t.slug),console.log(`Successfully reconciled product: ${e}`),{success:!0,message:`Successfully reconciled product: ${e}`,productHandle:e,product:r};return{success:!1,message:`Failed to normalize product: ${e}`,productHandle:e}}catch(t){return console.error(`Error reconciling product ${e}:`,t),{success:!1,message:`Failed to reconcile product ${e}: ${t}`,productHandle:e}}}s=(a.then?(await a)():a)[0],o()}catch(e){o(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,5972,4766,4868,7708,9910],()=>r(80543));module.exports=o})();