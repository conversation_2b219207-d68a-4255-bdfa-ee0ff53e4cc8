"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/route";
exports.ids = ["app/api/auth/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "graphql-request":
/*!**********************************!*\
  !*** external "graphql-request" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("graphql-request");;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_ankkorwoo_ankkor_src_app_api_auth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/route.ts */ \"(rsc)/./src/app/api/auth/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([E_ankkorwoo_ankkor_src_app_api_auth_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nE_ankkorwoo_ankkor_src_app_api_auth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/route\",\n        pathname: \"/api/auth\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/route\"\n    },\n    resolvedPagePath: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\api\\\\auth\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_ankkorwoo_ankkor_src_app_api_auth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZhdXRoJTJGcm91dGUmcGFnZT0lMkZhcGklMkZhdXRoJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGYXV0aCUyRnJvdXRlLnRzJmFwcERpcj1FJTNBJTVDYW5ra29yd29vJTVDYW5ra29yJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1FJTNBJTVDYW5ra29yd29vJTVDYW5ra29yJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PXN0YW5kYWxvbmUmcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDSTtBQUNqRjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlFQUFpRTtBQUN6RTtBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ3VIOztBQUV2SCxxQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Fua2tvci8/ZWM5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJFOlxcXFxhbmtrb3J3b29cXFxcYW5ra29yXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGF1dGhcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwic3RhbmRhbG9uZVwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9hdXRoL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYXV0aFwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYXV0aC9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkU6XFxcXGFua2tvcndvb1xcXFxhbmtrb3JcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYXV0aFxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvYXV0aC9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBvcmlnaW5hbFBhdGhuYW1lLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/auth/route.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var graphql_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! graphql-request */ \"graphql-request\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jwt-decode */ \"(rsc)/./node_modules/jwt-decode/build/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([graphql_request__WEBPACK_IMPORTED_MODULE_2__]);\ngraphql_request__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Auth token cookie name\nconst AUTH_COOKIE_NAME = \"woo_auth_token\";\nconst REFRESH_COOKIE_NAME = \"woo_refresh_token\";\n// GraphQL endpoint\nconst endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\";\nconst graphQLClient = new graphql_request__WEBPACK_IMPORTED_MODULE_2__.GraphQLClient(endpoint, {\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Make sure the URL has https:// prefix\nif (endpoint && !endpoint.startsWith(\"http\")) {\n    console.warn(\"GraphQL endpoint URL does not start with http(s)://, adding https:// prefix\");\n    graphQLClient.setEndpoint(`https://${endpoint}`);\n}\n// Login mutation\nconst LOGIN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_2__.gql)`\n  mutation LoginUser($username: String!, $password: String!) {\n    login(input: {\n      clientMutationId: \"login\"\n      username: $username\n      password: $password\n    }) {\n      authToken\n      refreshToken\n      user {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n      }\n    }\n  }\n`;\n// Register mutation\nconst REGISTER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_2__.gql)`\n  mutation RegisterUser($input: RegisterCustomerInput!) {\n    registerCustomer(input: $input) {\n      clientMutationId\n      authToken\n      refreshToken\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n      }\n    }\n  }\n`;\n// Refresh token mutation\nconst REFRESH_TOKEN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_2__.gql)`\n  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {\n    refreshJwtAuthToken(input: $input) {\n      authToken\n    }\n  }\n`;\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, ...data } = body;\n        switch(action){\n            case \"login\":\n                return handleLogin(data);\n            case \"register\":\n                return handleRegister(data);\n            case \"logout\":\n                return handleLogout();\n            case \"refresh\":\n                return handleRefreshToken(data);\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Invalid action\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Auth API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function handleLogin({ username, password }) {\n    try {\n        console.log(`Attempting login for user: ${username}`);\n        console.log(`Using GraphQL endpoint: ${endpoint}`);\n        // GraphQL request to login\n        const data = await graphQLClient.request(LOGIN_MUTATION, {\n            username,\n            password\n        });\n        console.log(\"Login response received:\", JSON.stringify(data, null, 2));\n        if (!data || !data.login || !data.login.authToken) {\n            console.error(\"Login failed: No auth token returned\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Invalid credentials\"\n            }, {\n                status: 401\n            });\n        }\n        // Set auth tokens as HTTP-only cookies\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        // Configure cookie options\n        const cookieOptions = {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"lax\",\n            path: \"/\",\n            maxAge: 60 * 60 * 24 * 7\n        };\n        // Set auth token cookie\n        cookieStore.set(AUTH_COOKIE_NAME, data.login.authToken, cookieOptions);\n        // Set refresh token cookie if available\n        if (data.login.refreshToken) {\n            cookieStore.set(REFRESH_COOKIE_NAME, data.login.refreshToken, {\n                ...cookieOptions,\n                maxAge: 60 * 60 * 24 * 30\n            });\n        }\n        console.log(\"Login successful, cookies set for user:\", data.login.user.email);\n        // Create response with user data and token for frontend use\n        const response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true,\n            user: {\n                id: data.login.user.id,\n                email: data.login.user.email,\n                firstName: data.login.user.firstName,\n                lastName: data.login.user.lastName\n            },\n            token: data.login.authToken // Include token for frontend access\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        // Check for specific GraphQL errors\n        if (error.response?.errors) {\n            const graphqlErrors = error.response.errors;\n            console.error(\"GraphQL errors:\", graphqlErrors);\n            // Check for specific error messages\n            const errorMessage = graphqlErrors[0]?.message || \"Login failed\";\n            if (errorMessage.includes(\"Invalid username\") || errorMessage.includes(\"incorrect password\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Invalid username or password\"\n                }, {\n                    status: 401\n                });\n            }\n            // Return first error message\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: errorMessage\n            }, {\n                status: 401\n            });\n        }\n        // Network or other errors\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Login failed. Please check your connection and try again.\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function handleRegister(userData) {\n    try {\n        const { email, firstName, lastName, password } = userData;\n        // Prepare input data for registration\n        const input = {\n            clientMutationId: \"registerCustomer\",\n            email,\n            firstName,\n            lastName,\n            password,\n            username: email\n        };\n        const data = await graphQLClient.request(REGISTER_MUTATION, {\n            input\n        });\n        if (!data.registerCustomer || !data.registerCustomer.authToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Registration failed\"\n            }, {\n                status: 400\n            });\n        }\n        // Set auth tokens as HTTP-only cookies\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        // Configure cookie options\n        const cookieOptions = {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"lax\",\n            path: \"/\",\n            maxAge: 60 * 60 * 24 * 7\n        };\n        // Set auth token cookie\n        cookieStore.set(AUTH_COOKIE_NAME, data.registerCustomer.authToken, cookieOptions);\n        // Set refresh token cookie if available\n        if (data.registerCustomer.refreshToken) {\n            cookieStore.set(REFRESH_COOKIE_NAME, data.registerCustomer.refreshToken, {\n                ...cookieOptions,\n                maxAge: 60 * 60 * 24 * 30\n            });\n        }\n        console.log(\"Registration successful, cookies set for user:\", email);\n        // Include token in response for frontend use\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true,\n            customer: data.registerCustomer.customer,\n            token: data.registerCustomer.authToken\n        });\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        // Check for specific GraphQL errors\n        if (error.response?.errors) {\n            const graphqlErrors = error.response.errors;\n            console.error(\"GraphQL errors:\", graphqlErrors);\n            // Return first error message\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: graphqlErrors[0]?.message || \"Registration failed\"\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Registration failed\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function handleLogout() {\n    // Clear auth cookies\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n    // Delete auth token cookie\n    cookieStore.set(AUTH_COOKIE_NAME, \"\", {\n        expires: new Date(0),\n        path: \"/\",\n        secure: \"development\" === \"production\",\n        httpOnly: true,\n        sameSite: \"lax\"\n    });\n    // Delete refresh token cookie\n    cookieStore.set(REFRESH_COOKIE_NAME, \"\", {\n        expires: new Date(0),\n        path: \"/\",\n        secure: \"development\" === \"production\",\n        httpOnly: true,\n        sameSite: \"lax\"\n    });\n    console.log(\"Logout: Auth cookies cleared\");\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n        success: true,\n        message: \"Logged out successfully\"\n    });\n}\nasync function handleRefreshToken({ refreshToken }) {\n    try {\n        // If no token provided, try to get from cookies\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const tokenFromCookie = cookieStore.get(REFRESH_COOKIE_NAME)?.value;\n        const token = refreshToken || tokenFromCookie;\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"No refresh token provided\"\n            }, {\n                status: 400\n            });\n        }\n        // Request a new auth token using the refresh token\n        const data = await graphQLClient.request(REFRESH_TOKEN_MUTATION, {\n            input: {\n                clientMutationId: \"refreshToken\",\n                jwtRefreshToken: token\n            }\n        });\n        if (!data.refreshJwtAuthToken || !data.refreshJwtAuthToken.authToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Failed to refresh token\"\n            }, {\n                status: 400\n            });\n        }\n        const newAuthToken = data.refreshJwtAuthToken.authToken;\n        // Set the new auth token as an HTTP-only cookie\n        cookieStore.set(AUTH_COOKIE_NAME, newAuthToken, {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"lax\",\n            path: \"/\",\n            maxAge: 60 * 60 * 24 * 7\n        });\n        // Try to extract user information from token\n        let userId = \"unknown\";\n        try {\n            const decodedToken = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_3__.jwtDecode)(newAuthToken);\n            userId = decodedToken.data?.user?.id || \"unknown\";\n        } catch (decodeError) {\n            console.error(\"Error decoding JWT token:\", decodeError);\n        }\n        console.log(\"Token refreshed successfully for user ID:\", userId);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Token refresh error:\", error);\n        // Check for specific GraphQL errors\n        if (error.response?.errors) {\n            const graphqlErrors = error.response.errors;\n            console.error(\"GraphQL errors:\", graphqlErrors);\n            // If the refresh token is invalid, clear the cookies\n            const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n            cookieStore.delete(AUTH_COOKIE_NAME);\n            cookieStore.delete(REFRESH_COOKIE_NAME);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: graphqlErrors[0]?.message || \"Token refresh failed\"\n            }, {\n                status: 401\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Token refresh failed\"\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXVDO0FBQ0k7QUFDVTtBQUNkO0FBRXZDLHlCQUF5QjtBQUN6QixNQUFNSyxtQkFBbUI7QUFDekIsTUFBTUMsc0JBQXNCO0FBRTVCLG1CQUFtQjtBQUNuQixNQUFNQyxXQUFXQyxRQUFRQyxHQUFHLENBQUNDLHVCQUF1QixJQUFJO0FBQ3hELE1BQU1DLGdCQUFnQixJQUFJVCwwREFBYUEsQ0FBQ0ssVUFBVTtJQUNoREssU0FBUztRQUNQLGdCQUFnQjtJQUNsQjtBQUNGO0FBRUEsd0NBQXdDO0FBQ3hDLElBQUlMLFlBQVksQ0FBQ0EsU0FBU00sVUFBVSxDQUFDLFNBQVM7SUFDNUNDLFFBQVFDLElBQUksQ0FBQztJQUNiSixjQUFjSyxXQUFXLENBQUMsQ0FBQyxRQUFRLEVBQUVULFNBQVMsQ0FBQztBQUNqRDtBQUVBLGlCQUFpQjtBQUNqQixNQUFNVSxpQkFBaUJkLG9EQUFHLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWtCM0IsQ0FBQztBQUVELG9CQUFvQjtBQUNwQixNQUFNZSxvQkFBb0JmLG9EQUFHLENBQUM7Ozs7Ozs7Ozs7Ozs7OztBQWU5QixDQUFDO0FBRUQseUJBQXlCO0FBQ3pCLE1BQU1nQix5QkFBeUJoQixvREFBRyxDQUFDOzs7Ozs7QUFNbkMsQ0FBQztBQUVNLGVBQWVpQixLQUFLQyxPQUFnQjtJQUN6QyxJQUFJO1FBQ0YsTUFBTUMsT0FBTyxNQUFNRCxRQUFRRSxJQUFJO1FBQy9CLE1BQU0sRUFBRUMsTUFBTSxFQUFFLEdBQUdDLE1BQU0sR0FBR0g7UUFFNUIsT0FBUUU7WUFDTixLQUFLO2dCQUNILE9BQU9FLFlBQVlEO1lBQ3JCLEtBQUs7Z0JBQ0gsT0FBT0UsZUFBZUY7WUFDeEIsS0FBSztnQkFDSCxPQUFPRztZQUNULEtBQUs7Z0JBQ0gsT0FBT0MsbUJBQW1CSjtZQUM1QjtnQkFDRSxPQUFPeEIscURBQVlBLENBQUNzQixJQUFJLENBQ3RCO29CQUFFTyxTQUFTO29CQUFPQyxTQUFTO2dCQUFpQixHQUM1QztvQkFBRUMsUUFBUTtnQkFBSTtRQUVwQjtJQUNGLEVBQUUsT0FBT0MsT0FBWTtRQUNuQm5CLFFBQVFtQixLQUFLLENBQUMsbUJBQW1CQTtRQUNqQyxPQUFPaEMscURBQVlBLENBQUNzQixJQUFJLENBQ3RCO1lBQUVPLFNBQVM7WUFBT0MsU0FBU0UsTUFBTUYsT0FBTyxJQUFJO1FBQWUsR0FDM0Q7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSxlQUFlTixZQUFZLEVBQUVRLFFBQVEsRUFBRUMsUUFBUSxFQUEwQztJQUN2RixJQUFJO1FBQ0ZyQixRQUFRc0IsR0FBRyxDQUFDLENBQUMsMkJBQTJCLEVBQUVGLFNBQVMsQ0FBQztRQUNwRHBCLFFBQVFzQixHQUFHLENBQUMsQ0FBQyx3QkFBd0IsRUFBRTdCLFNBQVMsQ0FBQztRQUVqRCwyQkFBMkI7UUFDM0IsTUFBTWtCLE9BQU8sTUFBTWQsY0FBY1UsT0FBTyxDQU1yQ0osZ0JBQWdCO1lBQ2pCaUI7WUFDQUM7UUFDRjtRQUVBckIsUUFBUXNCLEdBQUcsQ0FBQyw0QkFBNEJDLEtBQUtDLFNBQVMsQ0FBQ2IsTUFBTSxNQUFNO1FBRW5FLElBQUksQ0FBQ0EsUUFBUSxDQUFDQSxLQUFLYyxLQUFLLElBQUksQ0FBQ2QsS0FBS2MsS0FBSyxDQUFDQyxTQUFTLEVBQUU7WUFDakQxQixRQUFRbUIsS0FBSyxDQUFDO1lBQ2QsT0FBT2hDLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUN0QjtnQkFBRU8sU0FBUztnQkFBT0MsU0FBUztZQUFzQixHQUNqRDtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsdUNBQXVDO1FBQ3ZDLE1BQU1TLGNBQWN6QyxxREFBT0E7UUFFM0IsMkJBQTJCO1FBQzNCLE1BQU0wQyxnQkFBZ0I7WUFDcEJDLFVBQVU7WUFDVkMsUUFBUXBDLGtCQUF5QjtZQUNqQ3FDLFVBQVU7WUFDVkMsTUFBTTtZQUNOQyxRQUFRLEtBQUssS0FBSyxLQUFLO1FBQ3pCO1FBRUEsd0JBQXdCO1FBQ3hCTixZQUFZTyxHQUFHLENBQUMzQyxrQkFBa0JvQixLQUFLYyxLQUFLLENBQUNDLFNBQVMsRUFBRUU7UUFFeEQsd0NBQXdDO1FBQ3hDLElBQUlqQixLQUFLYyxLQUFLLENBQUNVLFlBQVksRUFBRTtZQUMzQlIsWUFBWU8sR0FBRyxDQUFDMUMscUJBQXFCbUIsS0FBS2MsS0FBSyxDQUFDVSxZQUFZLEVBQUU7Z0JBQzVELEdBQUdQLGFBQWE7Z0JBQ2hCSyxRQUFRLEtBQUssS0FBSyxLQUFLO1lBQ3pCO1FBQ0Y7UUFFQWpDLFFBQVFzQixHQUFHLENBQUMsMkNBQTJDWCxLQUFLYyxLQUFLLENBQUNXLElBQUksQ0FBQ0MsS0FBSztRQUU1RSw0REFBNEQ7UUFDNUQsTUFBTUMsV0FBV25ELHFEQUFZQSxDQUFDc0IsSUFBSSxDQUFDO1lBQ2pDTyxTQUFTO1lBQ1RvQixNQUFNO2dCQUNKRyxJQUFJNUIsS0FBS2MsS0FBSyxDQUFDVyxJQUFJLENBQUNHLEVBQUU7Z0JBQ3RCRixPQUFPMUIsS0FBS2MsS0FBSyxDQUFDVyxJQUFJLENBQUNDLEtBQUs7Z0JBQzVCRyxXQUFXN0IsS0FBS2MsS0FBSyxDQUFDVyxJQUFJLENBQUNJLFNBQVM7Z0JBQ3BDQyxVQUFVOUIsS0FBS2MsS0FBSyxDQUFDVyxJQUFJLENBQUNLLFFBQVE7WUFDcEM7WUFDQUMsT0FBTy9CLEtBQUtjLEtBQUssQ0FBQ0MsU0FBUyxDQUFDLG9DQUFvQztRQUNsRTtRQUVBLE9BQU9ZO0lBQ1QsRUFBRSxPQUFPbkIsT0FBWTtRQUNuQm5CLFFBQVFtQixLQUFLLENBQUMsZ0JBQWdCQTtRQUU5QixvQ0FBb0M7UUFDcEMsSUFBSUEsTUFBTW1CLFFBQVEsRUFBRUssUUFBUTtZQUMxQixNQUFNQyxnQkFBZ0J6QixNQUFNbUIsUUFBUSxDQUFDSyxNQUFNO1lBQzNDM0MsUUFBUW1CLEtBQUssQ0FBQyxtQkFBbUJ5QjtZQUVqQyxvQ0FBb0M7WUFDcEMsTUFBTUMsZUFBZUQsYUFBYSxDQUFDLEVBQUUsRUFBRTNCLFdBQVc7WUFDbEQsSUFBSTRCLGFBQWFDLFFBQVEsQ0FBQyx1QkFBdUJELGFBQWFDLFFBQVEsQ0FBQyx1QkFBdUI7Z0JBQzVGLE9BQU8zRCxxREFBWUEsQ0FBQ3NCLElBQUksQ0FDdEI7b0JBQUVPLFNBQVM7b0JBQU9DLFNBQVM7Z0JBQStCLEdBQzFEO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1lBRUEsNkJBQTZCO1lBQzdCLE9BQU8vQixxREFBWUEsQ0FBQ3NCLElBQUksQ0FDdEI7Z0JBQ0VPLFNBQVM7Z0JBQ1RDLFNBQVM0QjtZQUNYLEdBQ0E7Z0JBQUUzQixRQUFRO1lBQUk7UUFFbEI7UUFFQSwwQkFBMEI7UUFDMUIsT0FBTy9CLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUN0QjtZQUNFTyxTQUFTO1lBQ1RDLFNBQVNFLE1BQU1GLE9BQU8sSUFBSTtRQUM1QixHQUNBO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsZUFBZUwsZUFBZWtDLFFBQWE7SUFDekMsSUFBSTtRQUNGLE1BQU0sRUFBRVYsS0FBSyxFQUFFRyxTQUFTLEVBQUVDLFFBQVEsRUFBRXBCLFFBQVEsRUFBRSxHQUFHMEI7UUFFakQsc0NBQXNDO1FBQ3RDLE1BQU1DLFFBQVE7WUFDWkMsa0JBQWtCO1lBQ2xCWjtZQUNBRztZQUNBQztZQUNBcEI7WUFDQUQsVUFBVWlCO1FBQ1o7UUFFQSxNQUFNMUIsT0FBTyxNQUFNZCxjQUFjVSxPQUFPLENBTXJDSCxtQkFBbUI7WUFDcEI0QztRQUNGO1FBRUEsSUFBSSxDQUFDckMsS0FBS3VDLGdCQUFnQixJQUFJLENBQUN2QyxLQUFLdUMsZ0JBQWdCLENBQUN4QixTQUFTLEVBQUU7WUFDOUQsT0FBT3ZDLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUN0QjtnQkFBRU8sU0FBUztnQkFBT0MsU0FBUztZQUFzQixHQUNqRDtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsdUNBQXVDO1FBQ3ZDLE1BQU1TLGNBQWN6QyxxREFBT0E7UUFFM0IsMkJBQTJCO1FBQzNCLE1BQU0wQyxnQkFBZ0I7WUFDcEJDLFVBQVU7WUFDVkMsUUFBUXBDLGtCQUF5QjtZQUNqQ3FDLFVBQVU7WUFDVkMsTUFBTTtZQUNOQyxRQUFRLEtBQUssS0FBSyxLQUFLO1FBQ3pCO1FBRUEsd0JBQXdCO1FBQ3hCTixZQUFZTyxHQUFHLENBQUMzQyxrQkFBa0JvQixLQUFLdUMsZ0JBQWdCLENBQUN4QixTQUFTLEVBQUVFO1FBRW5FLHdDQUF3QztRQUN4QyxJQUFJakIsS0FBS3VDLGdCQUFnQixDQUFDZixZQUFZLEVBQUU7WUFDdENSLFlBQVlPLEdBQUcsQ0FBQzFDLHFCQUFxQm1CLEtBQUt1QyxnQkFBZ0IsQ0FBQ2YsWUFBWSxFQUFFO2dCQUN2RSxHQUFHUCxhQUFhO2dCQUNoQkssUUFBUSxLQUFLLEtBQUssS0FBSztZQUN6QjtRQUNGO1FBRUFqQyxRQUFRc0IsR0FBRyxDQUFDLGtEQUFrRGU7UUFFOUQsNkNBQTZDO1FBQzdDLE9BQU9sRCxxREFBWUEsQ0FBQ3NCLElBQUksQ0FBQztZQUN2Qk8sU0FBUztZQUNUbUMsVUFBVXhDLEtBQUt1QyxnQkFBZ0IsQ0FBQ0MsUUFBUTtZQUN4Q1QsT0FBTy9CLEtBQUt1QyxnQkFBZ0IsQ0FBQ3hCLFNBQVM7UUFDeEM7SUFDRixFQUFFLE9BQU9QLE9BQVk7UUFDbkJuQixRQUFRbUIsS0FBSyxDQUFDLHVCQUF1QkE7UUFFckMsb0NBQW9DO1FBQ3BDLElBQUlBLE1BQU1tQixRQUFRLEVBQUVLLFFBQVE7WUFDMUIsTUFBTUMsZ0JBQWdCekIsTUFBTW1CLFFBQVEsQ0FBQ0ssTUFBTTtZQUMzQzNDLFFBQVFtQixLQUFLLENBQUMsbUJBQW1CeUI7WUFFakMsNkJBQTZCO1lBQzdCLE9BQU96RCxxREFBWUEsQ0FBQ3NCLElBQUksQ0FDdEI7Z0JBQ0VPLFNBQVM7Z0JBQ1RDLFNBQVMyQixhQUFhLENBQUMsRUFBRSxFQUFFM0IsV0FBVztZQUN4QyxHQUNBO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxPQUFPL0IscURBQVlBLENBQUNzQixJQUFJLENBQ3RCO1lBQUVPLFNBQVM7WUFBT0MsU0FBU0UsTUFBTUYsT0FBTyxJQUFJO1FBQXNCLEdBQ2xFO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsZUFBZUo7SUFDYixxQkFBcUI7SUFDckIsTUFBTWEsY0FBY3pDLHFEQUFPQTtJQUUzQiwyQkFBMkI7SUFDM0J5QyxZQUFZTyxHQUFHLENBQUMzQyxrQkFBa0IsSUFBSTtRQUNwQzZELFNBQVMsSUFBSUMsS0FBSztRQUNsQnJCLE1BQU07UUFDTkYsUUFBUXBDLGtCQUF5QjtRQUNqQ21DLFVBQVU7UUFDVkUsVUFBVTtJQUNaO0lBRUEsOEJBQThCO0lBQzlCSixZQUFZTyxHQUFHLENBQUMxQyxxQkFBcUIsSUFBSTtRQUN2QzRELFNBQVMsSUFBSUMsS0FBSztRQUNsQnJCLE1BQU07UUFDTkYsUUFBUXBDLGtCQUF5QjtRQUNqQ21DLFVBQVU7UUFDVkUsVUFBVTtJQUNaO0lBRUEvQixRQUFRc0IsR0FBRyxDQUFDO0lBRVosT0FBT25DLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUFDO1FBQ3ZCTyxTQUFTO1FBQ1RDLFNBQVM7SUFDWDtBQUNGO0FBRUEsZUFBZUYsbUJBQW1CLEVBQUVvQixZQUFZLEVBQTZCO0lBQzNFLElBQUk7UUFDRixnREFBZ0Q7UUFDaEQsTUFBTVIsY0FBY3pDLHFEQUFPQTtRQUMzQixNQUFNb0Usa0JBQWtCM0IsWUFBWTRCLEdBQUcsQ0FBQy9ELHNCQUFzQmdFO1FBQzlELE1BQU1kLFFBQVFQLGdCQUFnQm1CO1FBRTlCLElBQUksQ0FBQ1osT0FBTztZQUNWLE9BQU92RCxxREFBWUEsQ0FBQ3NCLElBQUksQ0FDdEI7Z0JBQUVPLFNBQVM7Z0JBQU9DLFNBQVM7WUFBNEIsR0FDdkQ7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLG1EQUFtRDtRQUNuRCxNQUFNUCxPQUFPLE1BQU1kLGNBQWNVLE9BQU8sQ0FJckNGLHdCQUF3QjtZQUN6QjJDLE9BQU87Z0JBQ0xDLGtCQUFrQjtnQkFDbEJRLGlCQUFpQmY7WUFDbkI7UUFDRjtRQUVBLElBQUksQ0FBQy9CLEtBQUsrQyxtQkFBbUIsSUFBSSxDQUFDL0MsS0FBSytDLG1CQUFtQixDQUFDaEMsU0FBUyxFQUFFO1lBQ3BFLE9BQU92QyxxREFBWUEsQ0FBQ3NCLElBQUksQ0FDdEI7Z0JBQUVPLFNBQVM7Z0JBQU9DLFNBQVM7WUFBMEIsR0FDckQ7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU15QyxlQUFlaEQsS0FBSytDLG1CQUFtQixDQUFDaEMsU0FBUztRQUV2RCxnREFBZ0Q7UUFDaERDLFlBQVlPLEdBQUcsQ0FBQzNDLGtCQUFrQm9FLGNBQWM7WUFDOUM5QixVQUFVO1lBQ1ZDLFFBQVFwQyxrQkFBeUI7WUFDakNxQyxVQUFVO1lBQ1ZDLE1BQU07WUFDTkMsUUFBUSxLQUFLLEtBQUssS0FBSztRQUN6QjtRQUVBLDZDQUE2QztRQUM3QyxJQUFJMkIsU0FBUztRQUNiLElBQUk7WUFDRixNQUFNQyxlQUFldkUscURBQVNBLENBQU1xRTtZQUNwQ0MsU0FBU0MsYUFBYWxELElBQUksRUFBRXlCLE1BQU1HLE1BQU07UUFDMUMsRUFBRSxPQUFPdUIsYUFBYTtZQUNwQjlELFFBQVFtQixLQUFLLENBQUMsNkJBQTZCMkM7UUFDN0M7UUFFQTlELFFBQVFzQixHQUFHLENBQUMsNkNBQTZDc0M7UUFFekQsT0FBT3pFLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUFDO1lBQ3ZCTyxTQUFTO1FBQ1g7SUFDRixFQUFFLE9BQU9HLE9BQVk7UUFDbkJuQixRQUFRbUIsS0FBSyxDQUFDLHdCQUF3QkE7UUFFdEMsb0NBQW9DO1FBQ3BDLElBQUlBLE1BQU1tQixRQUFRLEVBQUVLLFFBQVE7WUFDMUIsTUFBTUMsZ0JBQWdCekIsTUFBTW1CLFFBQVEsQ0FBQ0ssTUFBTTtZQUMzQzNDLFFBQVFtQixLQUFLLENBQUMsbUJBQW1CeUI7WUFFakMscURBQXFEO1lBQ3JELE1BQU1qQixjQUFjekMscURBQU9BO1lBQzNCeUMsWUFBWW9DLE1BQU0sQ0FBQ3hFO1lBQ25Cb0MsWUFBWW9DLE1BQU0sQ0FBQ3ZFO1lBRW5CLE9BQU9MLHFEQUFZQSxDQUFDc0IsSUFBSSxDQUN0QjtnQkFDRU8sU0FBUztnQkFDVEMsU0FBUzJCLGFBQWEsQ0FBQyxFQUFFLEVBQUUzQixXQUFXO1lBQ3hDLEdBQ0E7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE9BQU8vQixxREFBWUEsQ0FBQ3NCLElBQUksQ0FDdEI7WUFBRU8sU0FBUztZQUFPQyxTQUFTRSxNQUFNRixPQUFPLElBQUk7UUFBdUIsR0FDbkU7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmtrb3IvLi9zcmMvYXBwL2FwaS9hdXRoL3JvdXRlLnRzP2E3NjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29va2llcyB9IGZyb20gJ25leHQvaGVhZGVycyc7XG5pbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBHcmFwaFFMQ2xpZW50LCBncWwgfSBmcm9tICdncmFwaHFsLXJlcXVlc3QnO1xuaW1wb3J0IHsgand0RGVjb2RlIH0gZnJvbSAnand0LWRlY29kZSc7XG5cbi8vIEF1dGggdG9rZW4gY29va2llIG5hbWVcbmNvbnN0IEFVVEhfQ09PS0lFX05BTUUgPSAnd29vX2F1dGhfdG9rZW4nO1xuY29uc3QgUkVGUkVTSF9DT09LSUVfTkFNRSA9ICd3b29fcmVmcmVzaF90b2tlbic7XG5cbi8vIEdyYXBoUUwgZW5kcG9pbnRcbmNvbnN0IGVuZHBvaW50ID0gcHJvY2Vzcy5lbnYuV09PQ09NTUVSQ0VfR1JBUEhRTF9VUkwgfHwgJ2h0dHBzOi8veW91ci13b3JkcHJlc3Mtc2l0ZS5jb20vZ3JhcGhxbCc7XG5jb25zdCBncmFwaFFMQ2xpZW50ID0gbmV3IEdyYXBoUUxDbGllbnQoZW5kcG9pbnQsIHtcbiAgaGVhZGVyczoge1xuICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gIH0sXG59KTtcblxuLy8gTWFrZSBzdXJlIHRoZSBVUkwgaGFzIGh0dHBzOi8vIHByZWZpeFxuaWYgKGVuZHBvaW50ICYmICFlbmRwb2ludC5zdGFydHNXaXRoKCdodHRwJykpIHtcbiAgY29uc29sZS53YXJuKCdHcmFwaFFMIGVuZHBvaW50IFVSTCBkb2VzIG5vdCBzdGFydCB3aXRoIGh0dHAocyk6Ly8sIGFkZGluZyBodHRwczovLyBwcmVmaXgnKTtcbiAgZ3JhcGhRTENsaWVudC5zZXRFbmRwb2ludChgaHR0cHM6Ly8ke2VuZHBvaW50fWApO1xufVxuXG4vLyBMb2dpbiBtdXRhdGlvblxuY29uc3QgTE9HSU5fTVVUQVRJT04gPSBncWxgXG4gIG11dGF0aW9uIExvZ2luVXNlcigkdXNlcm5hbWU6IFN0cmluZyEsICRwYXNzd29yZDogU3RyaW5nISkge1xuICAgIGxvZ2luKGlucHV0OiB7XG4gICAgICBjbGllbnRNdXRhdGlvbklkOiBcImxvZ2luXCJcbiAgICAgIHVzZXJuYW1lOiAkdXNlcm5hbWVcbiAgICAgIHBhc3N3b3JkOiAkcGFzc3dvcmRcbiAgICB9KSB7XG4gICAgICBhdXRoVG9rZW5cbiAgICAgIHJlZnJlc2hUb2tlblxuICAgICAgdXNlciB7XG4gICAgICAgIGlkXG4gICAgICAgIGRhdGFiYXNlSWRcbiAgICAgICAgZW1haWxcbiAgICAgICAgZmlyc3ROYW1lXG4gICAgICAgIGxhc3ROYW1lXG4gICAgICB9XG4gICAgfVxuICB9XG5gO1xuXG4vLyBSZWdpc3RlciBtdXRhdGlvblxuY29uc3QgUkVHSVNURVJfTVVUQVRJT04gPSBncWxgXG4gIG11dGF0aW9uIFJlZ2lzdGVyVXNlcigkaW5wdXQ6IFJlZ2lzdGVyQ3VzdG9tZXJJbnB1dCEpIHtcbiAgICByZWdpc3RlckN1c3RvbWVyKGlucHV0OiAkaW5wdXQpIHtcbiAgICAgIGNsaWVudE11dGF0aW9uSWRcbiAgICAgIGF1dGhUb2tlblxuICAgICAgcmVmcmVzaFRva2VuXG4gICAgICBjdXN0b21lciB7XG4gICAgICAgIGlkXG4gICAgICAgIGRhdGFiYXNlSWRcbiAgICAgICAgZW1haWxcbiAgICAgICAgZmlyc3ROYW1lXG4gICAgICAgIGxhc3ROYW1lXG4gICAgICB9XG4gICAgfVxuICB9XG5gO1xuXG4vLyBSZWZyZXNoIHRva2VuIG11dGF0aW9uXG5jb25zdCBSRUZSRVNIX1RPS0VOX01VVEFUSU9OID0gZ3FsYFxuICBtdXRhdGlvbiBSZWZyZXNoQXV0aFRva2VuKCRpbnB1dDogUmVmcmVzaEp3dEF1dGhUb2tlbklucHV0ISkge1xuICAgIHJlZnJlc2hKd3RBdXRoVG9rZW4oaW5wdXQ6ICRpbnB1dCkge1xuICAgICAgYXV0aFRva2VuXG4gICAgfVxuICB9XG5gO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuICAgIGNvbnN0IHsgYWN0aW9uLCAuLi5kYXRhIH0gPSBib2R5O1xuXG4gICAgc3dpdGNoIChhY3Rpb24pIHtcbiAgICAgIGNhc2UgJ2xvZ2luJzpcbiAgICAgICAgcmV0dXJuIGhhbmRsZUxvZ2luKGRhdGEpO1xuICAgICAgY2FzZSAncmVnaXN0ZXInOlxuICAgICAgICByZXR1cm4gaGFuZGxlUmVnaXN0ZXIoZGF0YSk7XG4gICAgICBjYXNlICdsb2dvdXQnOlxuICAgICAgICByZXR1cm4gaGFuZGxlTG9nb3V0KCk7XG4gICAgICBjYXNlICdyZWZyZXNoJzpcbiAgICAgICAgcmV0dXJuIGhhbmRsZVJlZnJlc2hUb2tlbihkYXRhKTtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnSW52YWxpZCBhY3Rpb24nIH0sXG4gICAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICAgICk7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcignQXV0aCBBUEkgZXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgc3VjY2VzczogZmFsc2UsIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UgfHwgJ1NlcnZlciBlcnJvcicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuYXN5bmMgZnVuY3Rpb24gaGFuZGxlTG9naW4oeyB1c2VybmFtZSwgcGFzc3dvcmQgfTogeyB1c2VybmFtZTogc3RyaW5nOyBwYXNzd29yZDogc3RyaW5nIH0pIHtcbiAgdHJ5IHtcbiAgICBjb25zb2xlLmxvZyhgQXR0ZW1wdGluZyBsb2dpbiBmb3IgdXNlcjogJHt1c2VybmFtZX1gKTtcbiAgICBjb25zb2xlLmxvZyhgVXNpbmcgR3JhcGhRTCBlbmRwb2ludDogJHtlbmRwb2ludH1gKTtcbiAgICBcbiAgICAvLyBHcmFwaFFMIHJlcXVlc3QgdG8gbG9naW5cbiAgICBjb25zdCBkYXRhID0gYXdhaXQgZ3JhcGhRTENsaWVudC5yZXF1ZXN0PHtcbiAgICAgIGxvZ2luOiB7XG4gICAgICAgIGF1dGhUb2tlbjogc3RyaW5nO1xuICAgICAgICByZWZyZXNoVG9rZW4/OiBzdHJpbmc7XG4gICAgICAgIHVzZXI6IGFueTtcbiAgICAgIH1cbiAgICB9PihMT0dJTl9NVVRBVElPTiwge1xuICAgICAgdXNlcm5hbWUsXG4gICAgICBwYXNzd29yZCxcbiAgICB9KTtcbiAgICBcbiAgICBjb25zb2xlLmxvZygnTG9naW4gcmVzcG9uc2UgcmVjZWl2ZWQ6JywgSlNPTi5zdHJpbmdpZnkoZGF0YSwgbnVsbCwgMikpO1xuICAgIFxuICAgIGlmICghZGF0YSB8fCAhZGF0YS5sb2dpbiB8fCAhZGF0YS5sb2dpbi5hdXRoVG9rZW4pIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ2luIGZhaWxlZDogTm8gYXV0aCB0b2tlbiByZXR1cm5lZCcpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnSW52YWxpZCBjcmVkZW50aWFscycgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMSB9XG4gICAgICApO1xuICAgIH1cbiAgICBcbiAgICAvLyBTZXQgYXV0aCB0b2tlbnMgYXMgSFRUUC1vbmx5IGNvb2tpZXNcbiAgICBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcbiAgICBcbiAgICAvLyBDb25maWd1cmUgY29va2llIG9wdGlvbnNcbiAgICBjb25zdCBjb29raWVPcHRpb25zID0ge1xuICAgICAgaHR0cE9ubHk6IHRydWUsXG4gICAgICBzZWN1cmU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicsXG4gICAgICBzYW1lU2l0ZTogJ2xheCcgYXMgY29uc3QsXG4gICAgICBwYXRoOiAnLycsXG4gICAgICBtYXhBZ2U6IDYwICogNjAgKiAyNCAqIDcsIC8vIDEgd2Vla1xuICAgIH07XG4gICAgXG4gICAgLy8gU2V0IGF1dGggdG9rZW4gY29va2llXG4gICAgY29va2llU3RvcmUuc2V0KEFVVEhfQ09PS0lFX05BTUUsIGRhdGEubG9naW4uYXV0aFRva2VuLCBjb29raWVPcHRpb25zKTtcbiAgICBcbiAgICAvLyBTZXQgcmVmcmVzaCB0b2tlbiBjb29raWUgaWYgYXZhaWxhYmxlXG4gICAgaWYgKGRhdGEubG9naW4ucmVmcmVzaFRva2VuKSB7XG4gICAgICBjb29raWVTdG9yZS5zZXQoUkVGUkVTSF9DT09LSUVfTkFNRSwgZGF0YS5sb2dpbi5yZWZyZXNoVG9rZW4sIHtcbiAgICAgICAgLi4uY29va2llT3B0aW9ucyxcbiAgICAgICAgbWF4QWdlOiA2MCAqIDYwICogMjQgKiAzMCwgLy8gMzAgZGF5c1xuICAgICAgfSk7XG4gICAgfVxuICAgIFxuICAgIGNvbnNvbGUubG9nKCdMb2dpbiBzdWNjZXNzZnVsLCBjb29raWVzIHNldCBmb3IgdXNlcjonLCBkYXRhLmxvZ2luLnVzZXIuZW1haWwpO1xuICAgIFxuICAgIC8vIENyZWF0ZSByZXNwb25zZSB3aXRoIHVzZXIgZGF0YSBhbmQgdG9rZW4gZm9yIGZyb250ZW5kIHVzZVxuICAgIGNvbnN0IHJlc3BvbnNlID0gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIHVzZXI6IHtcbiAgICAgICAgaWQ6IGRhdGEubG9naW4udXNlci5pZCxcbiAgICAgICAgZW1haWw6IGRhdGEubG9naW4udXNlci5lbWFpbCxcbiAgICAgICAgZmlyc3ROYW1lOiBkYXRhLmxvZ2luLnVzZXIuZmlyc3ROYW1lLFxuICAgICAgICBsYXN0TmFtZTogZGF0YS5sb2dpbi51c2VyLmxhc3ROYW1lLFxuICAgICAgfSxcbiAgICAgIHRva2VuOiBkYXRhLmxvZ2luLmF1dGhUb2tlbiAvLyBJbmNsdWRlIHRva2VuIGZvciBmcm9udGVuZCBhY2Nlc3NcbiAgICB9KTtcbiAgICBcbiAgICByZXR1cm4gcmVzcG9uc2U7XG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCdMb2dpbiBlcnJvcjonLCBlcnJvcik7XG4gICAgXG4gICAgLy8gQ2hlY2sgZm9yIHNwZWNpZmljIEdyYXBoUUwgZXJyb3JzXG4gICAgaWYgKGVycm9yLnJlc3BvbnNlPy5lcnJvcnMpIHtcbiAgICAgIGNvbnN0IGdyYXBocWxFcnJvcnMgPSBlcnJvci5yZXNwb25zZS5lcnJvcnM7XG4gICAgICBjb25zb2xlLmVycm9yKCdHcmFwaFFMIGVycm9yczonLCBncmFwaHFsRXJyb3JzKTtcbiAgICAgIFxuICAgICAgLy8gQ2hlY2sgZm9yIHNwZWNpZmljIGVycm9yIG1lc3NhZ2VzXG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBncmFwaHFsRXJyb3JzWzBdPy5tZXNzYWdlIHx8ICdMb2dpbiBmYWlsZWQnO1xuICAgICAgaWYgKGVycm9yTWVzc2FnZS5pbmNsdWRlcygnSW52YWxpZCB1c2VybmFtZScpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygnaW5jb3JyZWN0IHBhc3N3b3JkJykpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgc3VjY2VzczogZmFsc2UsIG1lc3NhZ2U6ICdJbnZhbGlkIHVzZXJuYW1lIG9yIHBhc3N3b3JkJyB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MDEgfVxuICAgICAgICApO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBSZXR1cm4gZmlyc3QgZXJyb3IgbWVzc2FnZVxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IFxuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLCBcbiAgICAgICAgICBtZXNzYWdlOiBlcnJvck1lc3NhZ2VcbiAgICAgICAgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMSB9XG4gICAgICApO1xuICAgIH1cbiAgICBcbiAgICAvLyBOZXR3b3JrIG9yIG90aGVyIGVycm9yc1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgXG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLCBcbiAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSB8fCAnTG9naW4gZmFpbGVkLiBQbGVhc2UgY2hlY2sgeW91ciBjb25uZWN0aW9uIGFuZCB0cnkgYWdhaW4uJyBcbiAgICAgIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGhhbmRsZVJlZ2lzdGVyKHVzZXJEYXRhOiBhbnkpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGVtYWlsLCBmaXJzdE5hbWUsIGxhc3ROYW1lLCBwYXNzd29yZCB9ID0gdXNlckRhdGE7XG4gICAgXG4gICAgLy8gUHJlcGFyZSBpbnB1dCBkYXRhIGZvciByZWdpc3RyYXRpb25cbiAgICBjb25zdCBpbnB1dCA9IHtcbiAgICAgIGNsaWVudE11dGF0aW9uSWQ6IFwicmVnaXN0ZXJDdXN0b21lclwiLFxuICAgICAgZW1haWwsXG4gICAgICBmaXJzdE5hbWUsXG4gICAgICBsYXN0TmFtZSxcbiAgICAgIHBhc3N3b3JkLFxuICAgICAgdXNlcm5hbWU6IGVtYWlsLFxuICAgIH07XG4gICAgXG4gICAgY29uc3QgZGF0YSA9IGF3YWl0IGdyYXBoUUxDbGllbnQucmVxdWVzdDx7XG4gICAgICByZWdpc3RlckN1c3RvbWVyPzoge1xuICAgICAgICBhdXRoVG9rZW46IHN0cmluZztcbiAgICAgICAgcmVmcmVzaFRva2VuPzogc3RyaW5nO1xuICAgICAgICBjdXN0b21lcjogYW55O1xuICAgICAgfVxuICAgIH0+KFJFR0lTVEVSX01VVEFUSU9OLCB7XG4gICAgICBpbnB1dCxcbiAgICB9KTtcbiAgICBcbiAgICBpZiAoIWRhdGEucmVnaXN0ZXJDdXN0b21lciB8fCAhZGF0YS5yZWdpc3RlckN1c3RvbWVyLmF1dGhUb2tlbikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnUmVnaXN0cmF0aW9uIGZhaWxlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cbiAgICBcbiAgICAvLyBTZXQgYXV0aCB0b2tlbnMgYXMgSFRUUC1vbmx5IGNvb2tpZXNcbiAgICBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcbiAgICBcbiAgICAvLyBDb25maWd1cmUgY29va2llIG9wdGlvbnNcbiAgICBjb25zdCBjb29raWVPcHRpb25zID0ge1xuICAgICAgaHR0cE9ubHk6IHRydWUsXG4gICAgICBzZWN1cmU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicsXG4gICAgICBzYW1lU2l0ZTogJ2xheCcgYXMgY29uc3QsXG4gICAgICBwYXRoOiAnLycsXG4gICAgICBtYXhBZ2U6IDYwICogNjAgKiAyNCAqIDcsIC8vIDEgd2Vla1xuICAgIH07XG4gICAgXG4gICAgLy8gU2V0IGF1dGggdG9rZW4gY29va2llXG4gICAgY29va2llU3RvcmUuc2V0KEFVVEhfQ09PS0lFX05BTUUsIGRhdGEucmVnaXN0ZXJDdXN0b21lci5hdXRoVG9rZW4sIGNvb2tpZU9wdGlvbnMpO1xuICAgIFxuICAgIC8vIFNldCByZWZyZXNoIHRva2VuIGNvb2tpZSBpZiBhdmFpbGFibGVcbiAgICBpZiAoZGF0YS5yZWdpc3RlckN1c3RvbWVyLnJlZnJlc2hUb2tlbikge1xuICAgICAgY29va2llU3RvcmUuc2V0KFJFRlJFU0hfQ09PS0lFX05BTUUsIGRhdGEucmVnaXN0ZXJDdXN0b21lci5yZWZyZXNoVG9rZW4sIHtcbiAgICAgICAgLi4uY29va2llT3B0aW9ucyxcbiAgICAgICAgbWF4QWdlOiA2MCAqIDYwICogMjQgKiAzMCwgLy8gMzAgZGF5c1xuICAgICAgfSk7XG4gICAgfVxuICAgIFxuICAgIGNvbnNvbGUubG9nKCdSZWdpc3RyYXRpb24gc3VjY2Vzc2Z1bCwgY29va2llcyBzZXQgZm9yIHVzZXI6JywgZW1haWwpO1xuICAgIFxuICAgIC8vIEluY2x1ZGUgdG9rZW4gaW4gcmVzcG9uc2UgZm9yIGZyb250ZW5kIHVzZVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgY3VzdG9tZXI6IGRhdGEucmVnaXN0ZXJDdXN0b21lci5jdXN0b21lcixcbiAgICAgIHRva2VuOiBkYXRhLnJlZ2lzdGVyQ3VzdG9tZXIuYXV0aFRva2VuXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCdSZWdpc3RyYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgIFxuICAgIC8vIENoZWNrIGZvciBzcGVjaWZpYyBHcmFwaFFMIGVycm9yc1xuICAgIGlmIChlcnJvci5yZXNwb25zZT8uZXJyb3JzKSB7XG4gICAgICBjb25zdCBncmFwaHFsRXJyb3JzID0gZXJyb3IucmVzcG9uc2UuZXJyb3JzO1xuICAgICAgY29uc29sZS5lcnJvcignR3JhcGhRTCBlcnJvcnM6JywgZ3JhcGhxbEVycm9ycyk7XG4gICAgICBcbiAgICAgIC8vIFJldHVybiBmaXJzdCBlcnJvciBtZXNzYWdlXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgXG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsIFxuICAgICAgICAgIG1lc3NhZ2U6IGdyYXBocWxFcnJvcnNbMF0/Lm1lc3NhZ2UgfHwgJ1JlZ2lzdHJhdGlvbiBmYWlsZWQnXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogZXJyb3IubWVzc2FnZSB8fCAnUmVnaXN0cmF0aW9uIGZhaWxlZCcgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuYXN5bmMgZnVuY3Rpb24gaGFuZGxlTG9nb3V0KCkge1xuICAvLyBDbGVhciBhdXRoIGNvb2tpZXNcbiAgY29uc3QgY29va2llU3RvcmUgPSBjb29raWVzKCk7XG4gIFxuICAvLyBEZWxldGUgYXV0aCB0b2tlbiBjb29raWVcbiAgY29va2llU3RvcmUuc2V0KEFVVEhfQ09PS0lFX05BTUUsICcnLCB7XG4gICAgZXhwaXJlczogbmV3IERhdGUoMCksXG4gICAgcGF0aDogJy8nLFxuICAgIHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJyxcbiAgICBodHRwT25seTogdHJ1ZSxcbiAgICBzYW1lU2l0ZTogJ2xheCcsXG4gIH0pO1xuICBcbiAgLy8gRGVsZXRlIHJlZnJlc2ggdG9rZW4gY29va2llXG4gIGNvb2tpZVN0b3JlLnNldChSRUZSRVNIX0NPT0tJRV9OQU1FLCAnJywge1xuICAgIGV4cGlyZXM6IG5ldyBEYXRlKDApLFxuICAgIHBhdGg6ICcvJyxcbiAgICBzZWN1cmU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicsXG4gICAgaHR0cE9ubHk6IHRydWUsXG4gICAgc2FtZVNpdGU6ICdsYXgnLFxuICB9KTtcbiAgXG4gIGNvbnNvbGUubG9nKCdMb2dvdXQ6IEF1dGggY29va2llcyBjbGVhcmVkJyk7XG4gIFxuICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBcbiAgICBzdWNjZXNzOiB0cnVlLCBcbiAgICBtZXNzYWdlOiAnTG9nZ2VkIG91dCBzdWNjZXNzZnVsbHknIFxuICB9KTtcbn1cblxuYXN5bmMgZnVuY3Rpb24gaGFuZGxlUmVmcmVzaFRva2VuKHsgcmVmcmVzaFRva2VuIH06IHsgcmVmcmVzaFRva2VuPzogc3RyaW5nIH0pIHtcbiAgdHJ5IHtcbiAgICAvLyBJZiBubyB0b2tlbiBwcm92aWRlZCwgdHJ5IHRvIGdldCBmcm9tIGNvb2tpZXNcbiAgICBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcbiAgICBjb25zdCB0b2tlbkZyb21Db29raWUgPSBjb29raWVTdG9yZS5nZXQoUkVGUkVTSF9DT09LSUVfTkFNRSk/LnZhbHVlO1xuICAgIGNvbnN0IHRva2VuID0gcmVmcmVzaFRva2VuIHx8IHRva2VuRnJvbUNvb2tpZTtcbiAgICBcbiAgICBpZiAoIXRva2VuKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgc3VjY2VzczogZmFsc2UsIG1lc3NhZ2U6ICdObyByZWZyZXNoIHRva2VuIHByb3ZpZGVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuICAgIFxuICAgIC8vIFJlcXVlc3QgYSBuZXcgYXV0aCB0b2tlbiB1c2luZyB0aGUgcmVmcmVzaCB0b2tlblxuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBncmFwaFFMQ2xpZW50LnJlcXVlc3Q8e1xuICAgICAgcmVmcmVzaEp3dEF1dGhUb2tlbjoge1xuICAgICAgICBhdXRoVG9rZW46IHN0cmluZztcbiAgICAgIH1cbiAgICB9PihSRUZSRVNIX1RPS0VOX01VVEFUSU9OLCB7XG4gICAgICBpbnB1dDoge1xuICAgICAgICBjbGllbnRNdXRhdGlvbklkOiBcInJlZnJlc2hUb2tlblwiLFxuICAgICAgICBqd3RSZWZyZXNoVG9rZW46IHRva2VuLFxuICAgICAgfSxcbiAgICB9KTtcbiAgICBcbiAgICBpZiAoIWRhdGEucmVmcmVzaEp3dEF1dGhUb2tlbiB8fCAhZGF0YS5yZWZyZXNoSnd0QXV0aFRva2VuLmF1dGhUb2tlbikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnRmFpbGVkIHRvIHJlZnJlc2ggdG9rZW4nIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG4gICAgXG4gICAgY29uc3QgbmV3QXV0aFRva2VuID0gZGF0YS5yZWZyZXNoSnd0QXV0aFRva2VuLmF1dGhUb2tlbjtcbiAgICBcbiAgICAvLyBTZXQgdGhlIG5ldyBhdXRoIHRva2VuIGFzIGFuIEhUVFAtb25seSBjb29raWVcbiAgICBjb29raWVTdG9yZS5zZXQoQVVUSF9DT09LSUVfTkFNRSwgbmV3QXV0aFRva2VuLCB7XG4gICAgICBodHRwT25seTogdHJ1ZSxcbiAgICAgIHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJyxcbiAgICAgIHNhbWVTaXRlOiAnbGF4JyxcbiAgICAgIHBhdGg6ICcvJyxcbiAgICAgIG1heEFnZTogNjAgKiA2MCAqIDI0ICogNywgLy8gMSB3ZWVrXG4gICAgfSk7XG4gICAgXG4gICAgLy8gVHJ5IHRvIGV4dHJhY3QgdXNlciBpbmZvcm1hdGlvbiBmcm9tIHRva2VuXG4gICAgbGV0IHVzZXJJZCA9ICd1bmtub3duJztcbiAgICB0cnkge1xuICAgICAgY29uc3QgZGVjb2RlZFRva2VuID0gand0RGVjb2RlPGFueT4obmV3QXV0aFRva2VuKTtcbiAgICAgIHVzZXJJZCA9IGRlY29kZWRUb2tlbi5kYXRhPy51c2VyPy5pZCB8fCAndW5rbm93bic7XG4gICAgfSBjYXRjaCAoZGVjb2RlRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlY29kaW5nIEpXVCB0b2tlbjonLCBkZWNvZGVFcnJvcik7XG4gICAgfVxuICAgIFxuICAgIGNvbnNvbGUubG9nKCdUb2tlbiByZWZyZXNoZWQgc3VjY2Vzc2Z1bGx5IGZvciB1c2VyIElEOicsIHVzZXJJZCk7XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCdUb2tlbiByZWZyZXNoIGVycm9yOicsIGVycm9yKTtcbiAgICBcbiAgICAvLyBDaGVjayBmb3Igc3BlY2lmaWMgR3JhcGhRTCBlcnJvcnNcbiAgICBpZiAoZXJyb3IucmVzcG9uc2U/LmVycm9ycykge1xuICAgICAgY29uc3QgZ3JhcGhxbEVycm9ycyA9IGVycm9yLnJlc3BvbnNlLmVycm9ycztcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0dyYXBoUUwgZXJyb3JzOicsIGdyYXBocWxFcnJvcnMpO1xuICAgICAgXG4gICAgICAvLyBJZiB0aGUgcmVmcmVzaCB0b2tlbiBpcyBpbnZhbGlkLCBjbGVhciB0aGUgY29va2llc1xuICAgICAgY29uc3QgY29va2llU3RvcmUgPSBjb29raWVzKCk7XG4gICAgICBjb29raWVTdG9yZS5kZWxldGUoQVVUSF9DT09LSUVfTkFNRSk7XG4gICAgICBjb29raWVTdG9yZS5kZWxldGUoUkVGUkVTSF9DT09LSUVfTkFNRSk7XG4gICAgICBcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSwgXG4gICAgICAgICAgbWVzc2FnZTogZ3JhcGhxbEVycm9yc1swXT8ubWVzc2FnZSB8fCAnVG9rZW4gcmVmcmVzaCBmYWlsZWQnXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDEgfVxuICAgICAgKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogZXJyb3IubWVzc2FnZSB8fCAnVG9rZW4gcmVmcmVzaCBmYWlsZWQnIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59ICJdLCJuYW1lcyI6WyJjb29raWVzIiwiTmV4dFJlc3BvbnNlIiwiR3JhcGhRTENsaWVudCIsImdxbCIsImp3dERlY29kZSIsIkFVVEhfQ09PS0lFX05BTUUiLCJSRUZSRVNIX0NPT0tJRV9OQU1FIiwiZW5kcG9pbnQiLCJwcm9jZXNzIiwiZW52IiwiV09PQ09NTUVSQ0VfR1JBUEhRTF9VUkwiLCJncmFwaFFMQ2xpZW50IiwiaGVhZGVycyIsInN0YXJ0c1dpdGgiLCJjb25zb2xlIiwid2FybiIsInNldEVuZHBvaW50IiwiTE9HSU5fTVVUQVRJT04iLCJSRUdJU1RFUl9NVVRBVElPTiIsIlJFRlJFU0hfVE9LRU5fTVVUQVRJT04iLCJQT1NUIiwicmVxdWVzdCIsImJvZHkiLCJqc29uIiwiYWN0aW9uIiwiZGF0YSIsImhhbmRsZUxvZ2luIiwiaGFuZGxlUmVnaXN0ZXIiLCJoYW5kbGVMb2dvdXQiLCJoYW5kbGVSZWZyZXNoVG9rZW4iLCJzdWNjZXNzIiwibWVzc2FnZSIsInN0YXR1cyIsImVycm9yIiwidXNlcm5hbWUiLCJwYXNzd29yZCIsImxvZyIsIkpTT04iLCJzdHJpbmdpZnkiLCJsb2dpbiIsImF1dGhUb2tlbiIsImNvb2tpZVN0b3JlIiwiY29va2llT3B0aW9ucyIsImh0dHBPbmx5Iiwic2VjdXJlIiwic2FtZVNpdGUiLCJwYXRoIiwibWF4QWdlIiwic2V0IiwicmVmcmVzaFRva2VuIiwidXNlciIsImVtYWlsIiwicmVzcG9uc2UiLCJpZCIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwidG9rZW4iLCJlcnJvcnMiLCJncmFwaHFsRXJyb3JzIiwiZXJyb3JNZXNzYWdlIiwiaW5jbHVkZXMiLCJ1c2VyRGF0YSIsImlucHV0IiwiY2xpZW50TXV0YXRpb25JZCIsInJlZ2lzdGVyQ3VzdG9tZXIiLCJjdXN0b21lciIsImV4cGlyZXMiLCJEYXRlIiwidG9rZW5Gcm9tQ29va2llIiwiZ2V0IiwidmFsdWUiLCJqd3RSZWZyZXNoVG9rZW4iLCJyZWZyZXNoSnd0QXV0aFRva2VuIiwibmV3QXV0aFRva2VuIiwidXNlcklkIiwiZGVjb2RlZFRva2VuIiwiZGVjb2RlRXJyb3IiLCJkZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jwt-decode"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();