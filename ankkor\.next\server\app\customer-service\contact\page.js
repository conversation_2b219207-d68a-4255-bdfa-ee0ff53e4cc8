(()=>{var e={};e.id=4645,e.ids=[4645],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},38684:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>n}),r(69601),r(52617),r(12523);var t=r(23191),a=r(88716),c=r(37922),l=r.n(c),o=r(95231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(s,i);let n=["",{children:["customer-service",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69601)),"E:\\ankkorwoo\\ankkor\\src\\app\\customer-service\\contact\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\customer-service\\contact\\page.tsx"],x="/customer-service/contact/page",m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/customer-service/contact/page",pathname:"/customer-service/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},34627:(e,s,r)=>{Promise.resolve().then(r.bind(r,31037))},48998:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},31037:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(10326);r(17577);var a=r(46226),c=r(90434),l=r(76557);let o=(0,l.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),i=(0,l.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),n=(0,l.Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var d=r(48998),x=r(54659),m=r(87888);let u=(0,l.Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);var h=r(60329),f=r(68211);let p=()=>{let e=process.env.NEXT_PUBLIC_FORMSPREE_FORM_ID||"xblgekrr",[s,r]=(0,h.cI)(e,{data:{form_type:"contact"}});return t.jsx("div",{className:"w-full max-w-2xl mx-auto",children:s.succeeded?(0,t.jsxs)("div",{className:"bg-[#f4f3f0] p-8 rounded-lg text-center",children:[t.jsx(x.Z,{className:"w-16 h-16 text-[#2c2c27] mx-auto mb-4"}),t.jsx("h3",{className:"font-serif text-2xl font-bold text-[#2c2c27] mb-2",children:"Message Sent"}),t.jsx("p",{className:"text-[#5c5c52] mb-6",children:"Thank you for contacting us. We have received your message and will respond shortly."}),t.jsx("button",{onClick:()=>window.location.reload(),className:"bg-[#2c2c27] text-[#f4f3f0] px-6 py-3 text-sm uppercase tracking-wider hover:bg-[#3d3d35] transition-colors",children:"Send Another Message"})]}):(0,t.jsxs)("form",{onSubmit:r,className:"space-y-6",children:[s.errors&&(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 p-4 rounded-lg flex items-start",children:[t.jsx(m.Z,{className:"w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"text-red-800 font-medium",children:"Error"}),t.jsx("p",{className:"text-red-700 text-sm",children:"There was a problem sending your message. Please try again later."})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"name",className:"block text-[#5c5c52] mb-1",children:"Name"}),t.jsx("input",{type:"text",id:"name",name:"name",className:"w-full border border-[#e5e2d9] bg-[#f8f8f5] p-3 focus:border-[#8a8778] focus:outline-none focus:ring-1 focus:ring-[#8a8778]",required:!0}),t.jsx(h.p8,{prefix:"Name",field:"name",errors:s.errors,className:"text-red-500 text-sm mt-1"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"email",className:"block text-[#5c5c52] mb-1",children:"Email"}),t.jsx("input",{type:"email",id:"email",name:"email",className:"w-full border border-[#e5e2d9] bg-[#f8f8f5] p-3 focus:border-[#8a8778] focus:outline-none focus:ring-1 focus:ring-[#8a8778]",required:!0}),t.jsx(h.p8,{prefix:"Email",field:"email",errors:s.errors,className:"text-red-500 text-sm mt-1"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"subject",className:"block text-[#5c5c52] mb-1",children:"Subject"}),t.jsx("input",{type:"text",id:"subject",name:"subject",className:"w-full border border-[#e5e2d9] bg-[#f8f8f5] p-3 focus:border-[#8a8778] focus:outline-none focus:ring-1 focus:ring-[#8a8778]",required:!0}),t.jsx(h.p8,{prefix:"Subject",field:"subject",errors:s.errors,className:"text-red-500 text-sm mt-1"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"message",className:"block text-[#5c5c52] mb-1",children:"Message"}),t.jsx("textarea",{id:"message",name:"message",rows:6,className:"w-full border border-[#e5e2d9] bg-[#f8f8f5] p-3 focus:border-[#8a8778] focus:outline-none focus:ring-1 focus:ring-[#8a8778]",required:!0}),t.jsx(h.p8,{prefix:"Message",field:"message",errors:s.errors,className:"text-red-500 text-sm mt-1"})]}),t.jsx("div",{className:"pt-2",children:t.jsx("button",{type:"submit",disabled:s.submitting,className:"bg-[#2c2c27] text-[#f4f3f0] px-8 py-3 flex items-center justify-center text-sm uppercase tracking-wider hover:bg-[#3d3d35] transition-colors disabled:opacity-70 disabled:cursor-not-allowed w-full md:w-auto",children:s.submitting?(0,t.jsxs)(t.Fragment,{children:[t.jsx(f.Z,{size:"sm",color:"#f4f3f0",className:"mr-2"}),"Sending..."]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(u,{className:"w-4 h-4 mr-2"}),"Send Message"]})})}),t.jsx(h.p8,{errors:s.errors})]})})};function b(){return(0,t.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,t.jsxs)("div",{className:"mb-12 text-center",children:[t.jsx("h1",{className:"mb-4 font-serif text-4xl font-bold text-[#2c2c27]",children:"Contact Us"}),t.jsx("p",{className:"mx-auto max-w-2xl text-[#5c5c52]",children:"We're here to assist you with any questions or concerns. Please feel free to reach out to us using the form below or through our contact information."})]}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-12",children:[(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("h2",{className:"mb-8 border-b border-[#e5e2d9] pb-4 font-serif text-2xl font-bold text-[#2c2c27]",children:"Send Us a Message"}),t.jsx(p,{})]}),(0,t.jsxs)("div",{className:"w-full lg:w-96",children:[t.jsx("h2",{className:"mb-8 border-b border-[#e5e2d9] pb-4 font-serif text-2xl font-bold text-[#2c2c27]",children:"Contact Information"}),(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[t.jsx(o,{className:"mr-4 h-5 w-5 text-[#8a8778]"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"mb-4 font-serif text-xl font-bold text-[#2c2c27]",children:"Email Us"}),t.jsx("p",{className:"mb-1 text-[#5c5c52]",children:t.jsx("a",{href:"mailto:<EMAIL>",className:"hover:text-[#2c2c27] hover:underline",children:"<EMAIL>"})}),t.jsx("p",{className:"text-[#5c5c52]"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[t.jsx(i,{className:"mr-4 h-5 w-5 text-[#8a8778]"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"mb-4 font-serif text-xl font-bold text-[#2c2c27]",children:"Call Us"}),t.jsx("p",{className:"mb-1 text-[#5c5c52]",children:t.jsx("a",{href:"tel:+91 9815319207",className:"hover:text-[#2c2c27] hover:underline",children:"+91 9815319207"})}),t.jsx("p",{className:"text-sm text-[#8a8778]",children:"Monday to Friday, 10am - 6pm IST"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[t.jsx(n,{className:"mr-4 h-5 w-5 text-[#8a8778]"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"mb-4 font-serif text-xl font-bold text-[#2c2c27]",children:"Operational Address:"}),t.jsx("p",{className:"mb-1 text-[#5c5c52]",children:"163/A Jagdish Nagar, St. 5 Avtar Market, Dugri Road"}),t.jsx("p",{className:"text-[#5c5c52]",children:"Ludhiana 141013, Punjab, India"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[t.jsx(d.Z,{className:"mr-4 h-5 w-5 text-[#8a8778]"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"mb-4 font-serif text-xl font-bold text-[#2c2c27]",children:"Business Hours"}),t.jsx("p",{className:"mb-1 text-[#5c5c52]",children:"Orders: 24*7"})]})]})]}),t.jsx("div",{className:"mt-10 overflow-hidden rounded-lg",children:t.jsx(a.default,{src:"https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80",alt:"Ankkor Store",width:400,height:300,className:"h-auto w-full image-animate transition-all duration-700"})})]})]}),(0,t.jsxs)("div",{className:"mt-16 border-t border-[#e5e2d9] pt-12 text-center",children:[t.jsx("h3",{className:"mb-4 font-serif text-2xl font-bold text-[#2c2c27]",children:"Have Questions?"}),t.jsx("p",{className:"mb-6 text-[#5c5c52]",children:"Check our frequently asked questions for quick answers to common inquiries."}),t.jsx(c.default,{href:"/customer-service/faq",className:"inline-block border border-[#2c2c27] px-8 py-3 text-sm uppercase tracking-wider text-[#2c2c27] transition-colors hover:bg-[#f4f3f0]",children:"View FAQ"})]})]})}},69601:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\customer-service\contact\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,2738,329,2993],()=>r(38684));module.exports=t})();