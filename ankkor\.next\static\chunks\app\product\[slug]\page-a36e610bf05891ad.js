(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1599],{54955:function(e,s,l){Promise.resolve().then(l.bind(l,50529))},50529:function(e,s,l){"use strict";l.d(s,{default:function(){return p}});var t=l(57437),a=l(2265),c=l(33145),r=l(43886),i=l(87758),n=l(12381),d=l(18686),o=l(39763);let u=(0,o.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),x=(0,o.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var m=l(42449),p=e=>{var s;let{product:l}=e,[o,p]=(0,a.useState)(0),[h,v]=(0,a.useState)(1),[g,j]=(0,a.useState)(null),[f,y]=(0,a.useState)({}),[N,b]=(0,a.useState)(!1),S=(0,i.rY)(),{openCart:w}=(0,d.j)(),{id:k,databaseId:_,name:C,description:T,shortDescription:I,price:U,regularPrice:E,onSale:M,stockStatus:A,image:q,galleryImages:O,attributes:z,type:L,variations:P}=l,Z="VARIABLE"===L,B=[(null==q?void 0:q.sourceUrl)?{sourceUrl:q.sourceUrl,altText:q.altText||C}:null,...(null==O?void 0:O.nodes)||[]].filter(Boolean),D=(e,s)=>{if(y(l=>({...l,[e]:s})),Z&&(null==P?void 0:P.nodes)){var l;let t={...f,[e]:s};if(null==z?void 0:null===(l=z.nodes)||void 0===l?void 0:l.every(e=>t[e.name])){let e=P.nodes.find(e=>e.attributes.nodes.every(e=>{let s=t[e.name];return e.value===s}));e?j(e):j(null)}}},H=async()=>{b(!0);try{var e,s;let l={productId:_.toString(),quantity:h,name:C,price:(null==g?void 0:g.price)||U,image:{url:(null===(e=B[0])||void 0===e?void 0:e.sourceUrl)||"",altText:(null===(s=B[0])||void 0===s?void 0:s.altText)||C}};await S.addToCart(l),w()}catch(e){console.error("Error adding product to cart:",e)}finally{b(!1)}},$="IN_STOCK"!==A,K=!Z||Z&&g;return(0,t.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden",children:(null===(s=B[o])||void 0===s?void 0:s.sourceUrl)&&(0,t.jsx)(c.default,{src:B[o].sourceUrl,alt:B[o].altText||C,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,className:"object-cover"})}),B.length>1&&(0,t.jsx)("div",{className:"grid grid-cols-5 gap-2",children:B.map((e,s)=>(0,t.jsx)("button",{onClick:()=>p(s),className:"relative aspect-square bg-[#f4f3f0] ".concat(o===s?"ring-2 ring-[#2c2c27]":""),children:(0,t.jsx)(c.default,{src:e.sourceUrl,alt:e.altText||"".concat(C," - Image ").concat(s+1),fill:!0,sizes:"(max-width: 768px) 20vw, 10vw",className:"object-cover"})},s))})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h1",{className:"text-3xl font-serif text-[#2c2c27]",children:C}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-xl font-medium text-[#2c2c27]",children:((null==g?void 0:g.price)||U).toString().includes("₹")||((null==g?void 0:g.price)||U).toString().includes("$")||((null==g?void 0:g.price)||U).toString().includes("€")||((null==g?void 0:g.price)||U).toString().includes("\xa3")?(null==g?void 0:g.price)||U:"₹".concat((null==g?void 0:g.price)||U)}),M&&E&&(0,t.jsx)("span",{className:"text-sm line-through text-[#8a8778]",children:E.toString().includes("₹")||E.toString().includes("$")||E.toString().includes("€")||E.toString().includes("\xa3")?E:"₹".concat(E)})]}),I&&(0,t.jsx)("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:I}}),Z&&(null==z?void 0:z.nodes)&&(0,t.jsx)("div",{className:"space-y-4",children:z.nodes.map(e=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:e.options.map(s=>(0,t.jsx)("button",{onClick:()=>D(e.name,s),className:"px-4 py-2 border ".concat(f[e.name]===s?"border-[#2c2c27] bg-[#2c2c27] text-white":"border-gray-300 hover:border-[#8a8778]"),children:s},s))})]},e.name))}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("span",{className:"text-[#5c5c52]",children:"Quantity:"}),(0,t.jsxs)("div",{className:"flex items-center border border-gray-300",children:[(0,t.jsx)("button",{onClick:()=>v(e=>e>1?e-1:1),disabled:h<=1,className:"px-3 py-2 hover:bg-gray-100","aria-label":"Decrease quantity",children:(0,t.jsx)(u,{className:"h-4 w-4"})}),(0,t.jsx)("span",{className:"px-4 py-2 border-x border-gray-300",children:h}),(0,t.jsx)("button",{onClick:()=>v(e=>e+1),className:"px-3 py-2 hover:bg-gray-100","aria-label":"Increase quantity",children:(0,t.jsx)(x,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-medium",children:"Availability: "}),(0,t.jsx)("span",{className:$?"text-red-600":"text-green-600",children:$?"Out of Stock":"In Stock"})]}),(0,t.jsxs)(r.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,t.jsxs)(n.z,{onClick:H,disabled:$||N||!K,className:"w-full py-6 bg-[#2c2c27] text-white hover:bg-[#3c3c37] flex items-center justify-center gap-2",children:[(0,t.jsx)(m.Z,{className:"h-5 w-5"}),N?"Adding...":"Add to Cart"]}),Z&&!K&&!$&&(0,t.jsx)("p",{className:"mt-2 text-sm text-red-600",children:"Please select all options to add this product to your cart"})]}),T&&(0,t.jsxs)("div",{className:"mt-12 border-t border-gray-200 pt-8",children:[(0,t.jsx)("h2",{className:"text-xl font-serif mb-4 text-[#2c2c27]",children:"Description"}),(0,t.jsx)("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:T}})]})]})]})})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,5717,8496,9429,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,6518,4595,3280,8790,1104,7158,6758,7044,5302,6628,5363,4754,1744],function(){return e(e.s=54955)}),_N_E=e.O()}]);