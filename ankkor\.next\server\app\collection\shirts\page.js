(()=>{var e={};e.id=9153,e.ids=[9153],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},34139:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>l}),r(5818),r(52617),r(12523);var s=r(23191),i=r(88716),n=r(37922),a=r.n(n),o=r(95231),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let l=["",{children:["collection",{children:["shirts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5818)),"E:\\ankkorwoo\\ankkor\\src\\app\\collection\\shirts\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\collection\\shirts\\page.tsx"],p="/collection/shirts/page",m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/collection/shirts/page",pathname:"/collection/shirts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},37973:(e,t,r)=>{Promise.resolve().then(r.bind(r,41383))},41137:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},41383:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>h});var i=r(10326),n=r(17577),a=r(46226),o=r(92148),c=r(41137),l=r(94019),d=r(53471),p=r(9512),m=r(15725),u=r(45107),x=e([d,m]);function h(){let[e,t]=(0,n.useState)([]),[r,s]=(0,n.useState)(!1),[x,h]=(0,n.useState)([0,25e3]),[g,f]=(0,n.useState)("featured"),[y,v]=(0,n.useState)(!0),[j,b]=(0,n.useState)(null);(0,p.Z)(y,"fabric");let N=()=>{s(!r)},w=[...e.filter(e=>{let t=parseFloat(e.price)||0;return t>=x[0]&&t<=x[1]})].sort((e,t)=>{switch(g){case"price-asc":return parseFloat(e.price)-parseFloat(t.price);case"price-desc":return parseFloat(t.price)-parseFloat(e.price);case"rating":return e.title.localeCompare(t.title);case"newest":return t.id.localeCompare(e.id);default:return 0}}),k={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5}},exit:{opacity:0,y:20,transition:{duration:.3}}};return(0,i.jsxs)("div",{className:"min-h-screen bg-[#f8f8f5] pt-8 pb-24",children:[i.jsx("div",{className:"container mx-auto px-4 mb-12",children:(0,i.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[i.jsx("h1",{className:"text-4xl font-serif font-bold mb-4 text-[#2c2c27]",children:"Shirts Collection"}),i.jsx("p",{className:"text-[#5c5c52] mb-8",children:"Discover our meticulously crafted shirts, designed with premium fabrics and impeccable attention to detail."})]})}),(0,i.jsxs)("div",{className:"relative h-[300px] mb-16 overflow-hidden",children:[i.jsx(a.default,{src:"https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?q=80",alt:"Ankkor Shirts Collection",fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover image-animate"}),i.jsx("div",{className:"absolute inset-0 bg-[#2c2c27] bg-opacity-30 flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center text-white",children:[i.jsx("h2",{className:"text-3xl font-serif font-bold mb-4",children:"Signature Shirts"}),i.jsx("p",{className:"text-lg max-w-xl mx-auto",children:"Impeccably tailored for the perfect fit"})]})})]}),(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[j&&(0,i.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 mb-8 rounded",children:[i.jsx("p",{children:j}),i.jsx("p",{className:"text-sm mt-2",children:"Please check your WooCommerce configuration in the .env.local file."})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center mb-8 md:hidden",children:[(0,i.jsxs)("button",{onClick:N,className:"flex items-center gap-2 text-[#2c2c27] border border-[#e5e2d9] px-4 py-2",children:[i.jsx(c.Z,{className:"h-4 w-4"}),i.jsx("span",{children:"Filter & Sort"})]}),(0,i.jsxs)("div",{className:"text-[#5c5c52] text-sm",children:[w.length," products"]})]}),r&&(0,i.jsxs)("div",{className:"fixed inset-0 z-50 md:hidden",children:[i.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:N}),(0,i.jsxs)("div",{className:"absolute right-0 top-0 bottom-0 w-80 bg-[#f8f8f5] p-6 overflow-auto",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[i.jsx("h3",{className:"font-serif text-lg text-[#2c2c27]",children:"Filter & Sort"}),i.jsx("button",{onClick:N,children:i.jsx(l.Z,{className:"h-5 w-5 text-[#2c2c27]"})})]}),(0,i.jsxs)("div",{className:"mb-8",children:[i.jsx("h4",{className:"text-[#8a8778] text-xs uppercase tracking-wider mb-4",children:"Price Range"}),(0,i.jsxs)("div",{className:"px-2",children:[(0,i.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,i.jsxs)("span",{className:"text-[#5c5c52] text-sm",children:[(0,u.jK)("INR"),x[0]]}),(0,i.jsxs)("span",{className:"text-[#5c5c52] text-sm",children:[(0,u.jK)("INR"),x[1]]})]}),i.jsx("input",{type:"range",min:"0",max:"25000",value:x[1],onChange:e=>h([x[0],parseInt(e.target.value)]),className:"w-full h-2 bg-[#e5e2d9] rounded-lg appearance-none cursor-pointer"})]})]}),(0,i.jsxs)("div",{children:[i.jsx("h4",{className:"text-[#8a8778] text-xs uppercase tracking-wider mb-4",children:"Sort By"}),i.jsx("div",{className:"space-y-3",children:[{id:"featured",name:"Featured"},{id:"price-asc",name:"Price: Low to High"},{id:"price-desc",name:"Price: High to Low"},{id:"rating",name:"Alphabetical"},{id:"newest",name:"Newest"}].map(e=>i.jsx("button",{onClick:()=>f(e.id),className:`block w-full text-left py-1 ${g===e.id?"text-[#2c2c27] font-medium":"text-[#5c5c52]"}`,children:e.name},e.id))})]}),i.jsx("button",{onClick:N,className:"w-full bg-[#2c2c27] text-[#f4f3f0] py-3 mt-8 text-sm uppercase tracking-wider",children:"Apply Filters"})]})]}),(0,i.jsxs)("div",{className:"flex flex-col md:flex-row gap-10",children:[i.jsx("div",{className:"hidden md:block w-64 shrink-0",children:(0,i.jsxs)("div",{className:"sticky top-24",children:[(0,i.jsxs)("div",{className:"mb-10",children:[i.jsx("h3",{className:"text-[#2c2c27] font-serif text-lg mb-6",children:"Price Range"}),(0,i.jsxs)("div",{className:"px-2",children:[(0,i.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,i.jsxs)("span",{className:"text-[#5c5c52]",children:[(0,u.jK)("INR"),x[0]]}),(0,i.jsxs)("span",{className:"text-[#5c5c52]",children:[(0,u.jK)("INR"),x[1]]})]}),i.jsx("input",{type:"range",min:"0",max:"25000",value:x[1],onChange:e=>h([x[0],parseInt(e.target.value)]),className:"w-full h-2 bg-[#e5e2d9] rounded-lg appearance-none cursor-pointer"})]})]}),(0,i.jsxs)("div",{children:[i.jsx("h3",{className:"text-[#2c2c27] font-serif text-lg mb-6",children:"Sort By"}),i.jsx("div",{className:"space-y-3",children:[{id:"featured",name:"Featured"},{id:"price-asc",name:"Price: Low to High"},{id:"price-desc",name:"Price: High to Low"},{id:"rating",name:"Alphabetical"},{id:"newest",name:"Newest"}].map(e=>i.jsx("button",{onClick:()=>f(e.id),className:`block w-full text-left py-1 ${g===e.id?"text-[#2c2c27] font-medium":"text-[#5c5c52] hover:text-[#2c2c27] transition-colors"}`,children:e.name},e.id))})]})]})}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"hidden md:flex justify-between items-center mb-8",children:[i.jsx("h2",{className:"text-[#2c2c27] font-serif text-xl",children:"Shirts Collection"}),(0,i.jsxs)("div",{className:"text-[#5c5c52]",children:[w.length," products"]})]}),i.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:w.map(e=>{let t="",r=!1;try{if(e.variants&&e.variants.length>0){let s=e.variants[0];if(s&&s.id){if(t=s.id,r=!0,!t.startsWith("gid://shopify/ProductVariant/")){let s=t.replace(/\D/g,"");s?t=`gid://shopify/ProductVariant/${s}`:(console.warn(`Cannot parse variant ID for product ${e.title}: ${t}`),r=!1)}console.log(`Product ${e.title} using variant ID: ${t}`)}}if(!r&&e.id&&e.id.includes("/")){let s=e.id.split("/"),i=s[s.length-1];i&&/^\d+$/.test(i)&&(t=`gid://shopify/ProductVariant/${i}`,console.warn(`Using fallback variant ID for ${e.title}: ${t}`),r=!0)}}catch(t){console.error(`Error processing variant for product ${e.title}:`,t),r=!1}return r||console.error(`No valid variant ID found for product: ${e.title}`),i.jsx(o.E.div,{variants:k,initial:"initial",animate:"animate",exit:"exit",layout:!0,children:i.jsx(d.Z,{id:e.id,name:e.title,price:e._originalWooProduct?.salePrice||e._originalWooProduct?.price||e.price,image:e.images[0]?.url||"",slug:e.handle,material:(0,m.mJ)(e,"custom_material",void 0,e.vendor||"Premium Fabric"),isNew:!0,stockStatus:e._originalWooProduct?.stockStatus||"IN_STOCK",currencySymbol:(0,u.jK)(e.currencyCode),currencyCode:e.currencyCode||"INR",compareAtPrice:e.compareAtPrice,regularPrice:e._originalWooProduct?.regularPrice,salePrice:e._originalWooProduct?.salePrice,onSale:e._originalWooProduct?.onSale||!1,shortDescription:e._originalWooProduct?.shortDescription,type:e._originalWooProduct?.type})},e.id)})}),0===w.length&&!y&&(0,i.jsxs)("div",{className:"text-center py-16",children:[i.jsx("p",{className:"text-[#5c5c52] mb-4",children:"No products found with the selected filters."}),i.jsx("button",{onClick:()=>{setSelectedMaterials([]),h([0,25e3])},className:"text-[#2c2c27] underline",children:"Reset filters"})]})]})]})]})]})}[d,m]=x.then?(await x)():x,s()}catch(e){s(e)}})},9512:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i}),r(17577);var s=r(2861);let i=function(e,t){let{setLoading:r,setVariant:i}=(0,s.r)()}},92079:(e,t,r)=>{"use strict";r.r(t),r.d(t,{addInventoryMapping:()=>d,clearInventoryMappings:()=>x,getAllInventoryMappings:()=>u,getProductHandleFromInventory:()=>p,loadInventoryMap:()=>c,saveInventoryMap:()=>l,updateInventoryMappings:()=>m});var s=r(78578);let i="inventory:mapping:",n=new s.s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),a={};function o(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function c(){if(!o())return{...a};try{let e=await n.keys(`${i}*`);if(0===e.length)return console.log("No existing inventory mappings found in Redis"),{};let t={},r=await n.mget(...e);return e.forEach((e,s)=>{let n=e.replace(i,""),a=r[s];t[n]=a}),console.log(`Loaded inventory mapping with ${Object.keys(t).length} entries from Redis`),t}catch(e){return console.error("Error loading inventory mapping from Redis:",e),console.log("Falling back to in-memory storage"),{...a}}}async function l(e){if(o())try{let t=n.pipeline(),r=await n.keys(`${i}*`);r.length>0&&t.del(...r),Object.entries(e).forEach(([e,r])=>{t.set(`${i}${e}`,r)}),await t.exec(),console.log(`Saved inventory mapping with ${Object.keys(e).length} entries to Redis`)}catch(t){console.error("Error saving inventory mapping to Redis:",t),console.log("Falling back to in-memory storage"),Object.assign(a,e)}else Object.assign(a,e),console.log(`Saved inventory mapping with ${Object.keys(e).length} entries to memory`)}async function d(e,t){try{return o()?(await n.set(`${i}${e}`,t),console.log(`Added mapping to Redis: ${e} -> ${t}`)):(a[e]=t,console.log(`Added mapping to memory: ${e} -> ${t}`)),!0}catch(r){console.error("Error adding inventory mapping:",r);try{return a[e]=t,console.log(`Added mapping to memory fallback: ${e} -> ${t}`),!0}catch(e){return console.error("Error adding to memory fallback:",e),!1}}}async function p(e){try{if(o())return await n.get(`${i}${e}`)||null;return a[e]||null}catch(t){console.error("Error getting product handle from Redis:",t);try{return a[e]||null}catch(e){return console.error("Error getting from memory fallback:",e),null}}}async function m(e){try{if(o()){let t=n.pipeline();for(let{inventoryItemId:r,productHandle:s}of e)t.set(`${i}${r}`,s);await t.exec(),console.log(`Updated ${e.length} inventory mappings in Redis`)}else{for(let{inventoryItemId:t,productHandle:r}of e)a[t]=r;console.log(`Updated ${e.length} inventory mappings in memory`)}return!0}catch(t){console.error("Error updating inventory mappings in Redis:",t);try{for(let{inventoryItemId:t,productHandle:r}of e)a[t]=r;return console.log(`Updated ${e.length} inventory mappings in memory fallback`),!0}catch(e){return console.error("Error updating in memory fallback:",e),!1}}}async function u(){return await c()}async function x(){try{if(o()){let e=await n.keys(`${i}*`);e.length>0&&await n.del(...e),console.log("Cleared all inventory mappings from Redis")}else Object.keys(a).forEach(e=>{delete a[e]}),console.log("Cleared all inventory mappings from memory");return!0}catch(e){return console.error("Error clearing inventory mappings:",e),!1}}},45107:(e,t,r)=>{"use strict";function s(e="INR"){switch(e){case"INR":return"₹";case"USD":return"$";case"EUR":return"€";case"GBP":return"\xa3";default:return e}}r.d(t,{jK:()=>s}),r(92079)},5818:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\collection\shirts\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,2738,2993,3471],()=>r(34139));module.exports=s})();