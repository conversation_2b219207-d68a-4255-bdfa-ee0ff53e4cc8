"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8787],{4707:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return P}});let l=n(47043),o=n(53099),r=n(57437),a=o._(n(2265)),u=l._(n(54887)),i=n(61956),s=n(44848),d=n(38137),c=n(61060),h=n(76015),f=n(7092),p=n(4123),g=n(80),y=n(73171),m=n(78505),x=n(28077),v=["bottom","height","left","right","top","width","x","y"];function R(e,t){let n=e.getBoundingClientRect();return n.top>=0&&n.top<=t}class S extends a.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){var n;if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,n)=>(0,h.matchSegment)(t,e[n]))))return;let l=null,o=e.hashFragment;if(o&&(l="top"===o?document.body:null!=(n=document.getElementById(o))?n:document.getElementsByName(o)[0]),l||(l="undefined"==typeof window?null:u.default.findDOMNode(this)),!(l instanceof Element))return;for(;!(l instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return v.every(e=>0===t[e])}(l);){if(null===l.nextElementSibling)return;l=l.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,f.handleSmoothScroll)(()=>{if(o){l.scrollIntoView();return}let e=document.documentElement,t=e.clientHeight;!R(l,t)&&(e.scrollTop=0,R(l,t)||l.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,l.focus()}}}}function C(e){let{segmentPath:t,children:n}=e,l=(0,a.useContext)(i.GlobalLayoutRouterContext);if(!l)throw Error("invariant global layout router not mounted");return(0,r.jsx)(S,{segmentPath:t,focusAndScrollRef:l.focusAndScrollRef,children:n})}function b(e){let{parallelRouterKey:t,url:n,childNodes:l,segmentPath:o,tree:u,cacheKey:c}=e,f=(0,a.useContext)(i.GlobalLayoutRouterContext);if(!f)throw Error("invariant global layout router not mounted");let{buildId:p,changeByServerResponse:g,tree:y}=f,m=l.get(c);if(void 0===m){let e={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null};m=e,l.set(c,e)}let v=null!==m.prefetchRsc?m.prefetchRsc:m.rsc,R=(0,a.useDeferredValue)(m.rsc,v),S="object"==typeof R&&null!==R&&"function"==typeof R.then?(0,a.use)(R):R;if(!S){let e=m.lazyData;if(null===e){let t=function e(t,n){if(t){let[l,o]=t,r=2===t.length;if((0,h.matchSegment)(n[0],l)&&n[1].hasOwnProperty(o)){if(r){let t=e(void 0,n[1][o]);return[n[0],{...n[1],[o]:[t[0],t[1],t[2],"refetch"]}]}return[n[0],{...n[1],[o]:e(t.slice(2),n[1][o])}]}}return n}(["",...o],y),l=(0,x.hasInterceptionRouteInCurrentTree)(y);m.lazyData=e=(0,s.fetchServerResponse)(new URL(n,location.origin),t,l?f.nextUrl:null,p),m.lazyDataResolved=!1}let t=(0,a.use)(e);m.lazyDataResolved||(setTimeout(()=>{(0,a.startTransition)(()=>{g({previousTree:y,serverResponse:t})})}),m.lazyDataResolved=!0),(0,a.use)(d.unresolvedThenable)}return(0,r.jsx)(i.LayoutRouterContext.Provider,{value:{tree:u[1][t],childNodes:m.parallelRoutes,url:n,loading:m.loading},children:S})}function j(e){let{children:t,hasLoading:n,loading:l,loadingStyles:o,loadingScripts:u}=e;return n?(0,r.jsx)(a.Suspense,{fallback:(0,r.jsxs)(r.Fragment,{children:[o,u,l]}),children:t}):(0,r.jsx)(r.Fragment,{children:t})}function P(e){let{parallelRouterKey:t,segmentPath:n,error:l,errorStyles:o,errorScripts:u,templateStyles:s,templateScripts:d,template:h,notFound:f,notFoundStyles:x}=e,v=(0,a.useContext)(i.LayoutRouterContext);if(!v)throw Error("invariant expected layout router to be mounted");let{childNodes:R,tree:S,url:P,loading:w}=v,E=R.get(t);E||(E=new Map,R.set(t,E));let _=S[1][t][0],D=(0,y.getSegmentValue)(_),F=[_];return(0,r.jsx)(r.Fragment,{children:F.map(e=>{let a=(0,y.getSegmentValue)(e),v=(0,m.createRouterCacheKey)(e);return(0,r.jsxs)(i.TemplateContext.Provider,{value:(0,r.jsx)(C,{segmentPath:n,children:(0,r.jsx)(c.ErrorBoundary,{errorComponent:l,errorStyles:o,errorScripts:u,children:(0,r.jsx)(j,{hasLoading:!!w,loading:null==w?void 0:w[0],loadingStyles:null==w?void 0:w[1],loadingScripts:null==w?void 0:w[2],children:(0,r.jsx)(g.NotFoundBoundary,{notFound:f,notFoundStyles:x,children:(0,r.jsx)(p.RedirectBoundary,{children:(0,r.jsx)(b,{parallelRouterKey:t,url:P,tree:S,childNodes:E,segmentPath:n,cacheKey:v,isActive:D===a})})})})})}),children:[s,d,h]},(0,m.createRouterCacheKey)(e,!0))})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);