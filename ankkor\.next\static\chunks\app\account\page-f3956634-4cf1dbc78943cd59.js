(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[447],{48742:function(e,t,r){Promise.resolve().then(r.bind(r,71047))},79820:function(e,t,r){"use strict";r.d(t,{Ol:function(){return l},SZ:function(){return c},Zb:function(){return s},aY:function(){return o},eW:function(){return d},ll:function(){return f}});var n=r(57437),a=r(2265),i=r(93448);let s=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border border-[#e5e2d9] bg-[#f8f8f5] text-[#2c2c27] shadow-sm",r),...a})});s.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...a})});l.displayName="CardHeader";let f=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});f.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-[#5c5c52]",r),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...a})});o.displayName="CardContent";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...a})});d.displayName="CardFooter"},17168:function(e,t,r){"use strict";r.d(t,{SP:function(){return c},dr:function(){return f},mQ:function(){return l},nU:function(){return o}});var n=r(57437),a=r(2265),i=r(32449),s=r(93448);let l=i.fC,f=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(i.aV,{ref:t,className:(0,s.cn)("inline-flex h-10 items-center justify-center rounded-md p-1 text-[#5c5c52]",r),...a})});f.displayName=i.aV.displayName;let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(i.xz,{ref:t,className:(0,s.cn)("inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#8a8778] focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:border-b-2 data-[state=active]:border-[#2c2c27] data-[state=active]:text-[#2c2c27] data-[state=active]:font-semibold",r),...a})});c.displayName=i.xz.displayName;let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(i.VY,{ref:t,className:(0,s.cn)("mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#8a8778] focus-visible:ring-offset-2",r),...a})});o.displayName=i.VY.displayName}}]);