(()=>{var e={};e.id=7928,e.ids=[7928],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},58188:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>o,routeModule:()=>g,tree:()=>d}),s(65413),s(52617),s(12523);var t=s(23191),a=s(88716),i=s(37922),n=s.n(i),c=s(95231),l={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);s.d(r,l);let d=["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,65413)),"E:\\ankkorwoo\\ankkor\\src\\app\\test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],o=["E:\\ankkorwoo\\ankkor\\src\\app\\test\\page.tsx"],u="/test/page",m={require:s,loadChunk:()=>Promise.resolve()},g=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},68302:(e,r,s)=>{Promise.resolve().then(s.bind(s,75072))},75290:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},75072:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{default:()=>g});var a=s(10326),i=s(17577),n=s(72248),c=s(15725),l=s(54337),d=s(32913),o=s(75290),u=e([c,l,d]);[c,l,d]=u.then?(await u)():u;let m=({title:e,children:r})=>(0,a.jsxs)("div",{className:"mb-8 border rounded-md p-4",children:[a.jsx("h2",{className:"text-lg font-medium mb-4",children:e}),r]}),g=()=>{let[e,r]=(0,i.useState)([]),[s,t]=(0,i.useState)([]),[u,g]=(0,i.useState)(null),[h,p]=(0,i.useState)(null),[x,v]=(0,i.useState)({}),[f,b]=(0,i.useState)({}),j=(0,l.xS)(),y=(e,r)=>{v(s=>({...s,[e]:r}))},N=(e,r)=>{b(s=>({...s,[e]:r}))},w=async()=>{try{y("products",!0);let e=await (0,c.Xp)();r(e.nodes||[]),N("products",`Success! Fetched ${e.nodes?.length||0} products`)}catch(e){console.error("Error fetching products:",e),N("products",`Error: ${e.message}`)}finally{y("products",!1)}},k=async()=>{try{y("categories",!0);let e=await (0,c.CP)();t(e.nodes||[]),N("categories",`Success! Fetched ${e.nodes?.length||0} categories`)}catch(e){console.error("Error fetching categories:",e),N("categories",`Error: ${e.message}`)}finally{y("categories",!1)}},E=async()=>{if(!e.length){N("product","Error: No products available to test with");return}try{y("product",!0);let r=e[0].databaseId,s=await (0,c.wv)(r);g(s),N("product",`Success! Fetched product: ${s.name}`)}catch(e){console.error("Error fetching product:",e),N("product",`Error: ${e.message}`)}finally{y("product",!1)}},$=async()=>{if(!e.length){N("cart","Error: No products available to test with");return}try{y("cart",!0);let r=e[0];await j.addToCart({productId:r.databaseId.toString(),name:r.name,price:r.price,quantity:1,image:{url:r.image?.sourceUrl||"",altText:r.image?.altText||r.name}}),N("cart",`Success! Added ${r.name} to cart`)}catch(e){console.error("Error adding to cart:",e),N("cart",`Error: ${e.message}`)}finally{y("cart",!1)}},C=async()=>{try{y("login",!0);let e=await (0,d.x4)("<EMAIL>","password123");e&&(p(e),N("login",`Success! Logged in as ${e.email}`))}catch(e){console.error("Error logging in:",e),N("login",`Error: ${e.message}`)}finally{y("login",!1)}},P=async()=>{try{y("register",!0);let e=`test${Math.floor(1e4*Math.random())}@example.com`;await (0,d.z2)({email:e,firstName:"Test",lastName:"User",password:"password123",username:`testuser${Math.floor(1e4*Math.random())}`}),N("register",`Success! Registered user: ${e}`)}catch(e){console.error("Error registering:",e),N("register",`Error: ${e.message}`)}finally{y("register",!1)}},S=async()=>{try{y("currentUser",!0);let e=await (0,d.ts)();e?(p(e),N("currentUser",`Success! Current user: ${e.email}`)):N("currentUser","No user is currently logged in")}catch(e){console.error("Error getting current user:",e),N("currentUser",`Error: ${e.message}`)}finally{y("currentUser",!1)}};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Integration Test"}),a.jsx(m,{title:"Products",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:w,disabled:x.products,children:[x.products&&a.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Products"]}),f.products&&a.jsx("div",{className:`p-3 rounded-md ${f.products.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.products}),e.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h3",{className:"font-medium mb-2",children:"First 5 Products:"}),a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:e.slice(0,5).map(e=>(0,a.jsxs)("li",{children:[e.name," - $",e.price]},e.id))})]})]})}),a.jsx(m,{title:"Categories",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:k,disabled:x.categories,children:[x.categories&&a.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Categories"]}),f.categories&&a.jsx("div",{className:`p-3 rounded-md ${f.categories.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.categories}),s.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h3",{className:"font-medium mb-2",children:"Categories:"}),a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:s.map(e=>a.jsx("li",{children:e.name},e.id))})]})]})}),a.jsx(m,{title:"Single Product",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:E,disabled:x.product||!e.length,children:[x.product&&a.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Single Product"]}),f.product&&a.jsx("div",{className:`p-3 rounded-md ${f.product.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.product}),u&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[a.jsx("h3",{className:"font-medium text-lg",children:u.name}),(0,a.jsxs)("p",{className:"text-gray-500 mt-1",children:["$",u.price]}),u.image&&a.jsx("div",{className:"mt-2 w-32 h-32 relative",children:a.jsx("img",{src:u.image.sourceUrl,alt:u.image.altText||u.name,className:"object-cover w-full h-full"})})]})]})}),a.jsx(m,{title:"Cart",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:$,disabled:x.cart||!e.length,children:[x.cart&&a.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Add to Cart"]}),f.cart&&a.jsx("div",{className:`p-3 rounded-md ${f.cart.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.cart}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("h3",{className:"font-medium mb-2",children:["Cart Items: ",j.items.length]}),j.items.length>0&&a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:j.items.map(e=>(0,a.jsxs)("li",{children:[e.name," - Qty: ",e.quantity]},e.id))})]})]})}),a.jsx(m,{title:"Authentication",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(n.z,{onClick:C,disabled:x.login,children:[x.login&&a.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Login"]}),(0,a.jsxs)(n.z,{onClick:P,disabled:x.register,variant:"outline",children:[x.register&&a.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Register"]}),(0,a.jsxs)(n.z,{onClick:S,disabled:x.currentUser,variant:"secondary",children:[x.currentUser&&a.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Get Current User"]})]}),f.login&&a.jsx("div",{className:`p-3 rounded-md ${f.login.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.login}),f.register&&a.jsx("div",{className:`p-3 rounded-md ${f.register.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.register}),f.currentUser&&a.jsx("div",{className:`p-3 rounded-md ${f.currentUser.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.currentUser}),h&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[a.jsx("h3",{className:"font-medium",children:"Current User:"}),(0,a.jsxs)("p",{children:["Email: ",h.email]}),(0,a.jsxs)("p",{children:["Name: ",h.firstName," ",h.lastName]})]})]})})]})};t()}catch(e){t(e)}})},72248:(e,r,s)=>{"use strict";let t,a;s.d(r,{z:()=>m});var i=s(10326);s(17577);var n=s(34214),c=s(41135);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,d=c.W;var o=s(51223);let u=(t="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a={variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}},e=>{var r;if((null==a?void 0:a.variants)==null)return d(t,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:s,defaultVariants:i}=a,n=Object.keys(s).map(r=>{let t=null==e?void 0:e[r],a=null==i?void 0:i[r];if(null===t)return null;let n=l(t)||l(a);return s[r][n]}),c=e&&Object.entries(e).reduce((e,r)=>{let[s,t]=r;return void 0===t||(e[s]=t),e},{});return d(t,n,null==a?void 0:null===(r=a.compoundVariants)||void 0===r?void 0:r.reduce((e,r)=>{let{class:s,className:t,...a}=r;return Object.entries(a).every(e=>{let[r,s]=e;return Array.isArray(s)?s.includes({...i,...c}[r]):({...i,...c})[r]===s})?[...e,s,t]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)});function m({className:e,variant:r,size:s,asChild:t=!1,...a}){let c=t?n.g7:"button";return i.jsx(c,{"data-slot":"button",className:(0,o.cn)(u({variant:r,size:s,className:e})),...a})}},32913:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{ts:()=>a.ts,x4:()=>a.x4,z2:()=>a.z2});var a=s(61296),i=e([a]);a=(i.then?(await i)():i)[0],t()}catch(e){t(e)}})},65413:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var t=s(19510);let a=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\test\WooCommerceTest.tsx#default`);function i(){return t.jsx(a,{})}}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8948,2738,2993],()=>s(58188));module.exports=t})();