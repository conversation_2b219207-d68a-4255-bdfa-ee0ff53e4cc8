(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4456],{49759:function(e,t,s){Promise.resolve().then(s.bind(s,32928))},32928:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return y}});var a=s(57437),r=s(2265),i=s(27648),l=s(33145),c=s(88997),n=s(32489);let d=(0,s(39763).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var o=s(42449),m=s(30401),h=s(92371),x=s(3371),u=s(29658),f=s(43886),p=s(11738),v=s(12381);let j=e=>{if("number"==typeof e)return e;if(!e)return 0;let t=parseFloat(e.toString().replace(/[^\d.-]/g,""));return isNaN(t)?0:t},g=e=>j(e).toFixed(2);function y(){let e=(0,h.x)(),{items:t,removeFromWishlist:s,clearWishlist:j}=(0,h.Y)(),{isAuthenticated:y,isLoading:N}=(0,x.O)(),[b,w]=(0,r.useState)(!0),[C,I]=(0,r.useState)({}),[k,A]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if(!y&&t.length>0&&!N&&"true"!==sessionStorage.getItem("wishlist_prompt_dismissed")){let e=setTimeout(()=>{A(!0)},3e3);return()=>clearTimeout(e)}},[y,t.length,N]),(0,r.useEffect)(()=>{if(!N){let e=setTimeout(()=>{w(!1)},500);return()=>clearTimeout(e)}},[N]);let F=function(t){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{if(!t.variantId||"string"!=typeof t.variantId||""===t.variantId.trim()){console.error("Invalid variant ID:",t.variantId),p.Am.error("Unable to add this item to your cart. Invalid product variant.");return}let r=t.variantId;if(!r.startsWith("gid://"))try{let e=r.replace(/\D/g,"");if(!e)throw Error('Could not extract a valid numeric ID from "'.concat(r,'"'));r="gid://shopify/ProductVariant/".concat(e)}catch(e){console.error("Failed to format variant ID:",e),p.Am.error("This product has an invalid variant ID format.");return}console.log("Adding item to cart: ".concat(t.name||"Unnamed Product"," with variant ID: ").concat(r)),e.addItem({productId:t.id,variantId:r,title:t.name||"Unnamed Product",handle:t.handle||"#",image:t.image||"/placeholder-image.jpg",price:t.price?g(t.price):"0.00",quantity:1,currencyCode:"INR"}).then(()=>{p.Am.success("".concat(t.name||"Product"," added to your cart!")),I(e=>({...e,[t.id]:!0})),setTimeout(()=>{I(e=>({...e,[t.id]:!1}))},2e3),a&&s(t.id)}).catch(e=>{var t,s;console.error("Error from cart.addItem:",e),(null===(t=e.message)||void 0===t?void 0:t.includes("variant is no longer available"))?p.Am.error("This product is no longer available in the store."):(null===(s=e.message)||void 0===s?void 0:s.includes("Invalid variant ID"))?p.Am.error("This product has an invalid variant format. Please try another item."):p.Am.error("Unable to add this item to your cart. Please try again later.")})}catch(e){console.error("Error in handleAddToCart:",e),p.Am.error("An unexpected error occurred. Please try again later.")}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-serif",children:"My Wishlist"}),t.length>0&&(0,a.jsx)(v.z,{variant:"outline",onClick:j,className:"text-sm",children:"Clear All"})]}),b?(0,a.jsx)("div",{className:"flex items-center justify-center py-24",children:(0,a.jsx)(u.Z,{size:"lg",color:"#8a8778"})}):(0,a.jsxs)(a.Fragment,{children:[!y&&t.length>0&&!k&&(0,a.jsx)("div",{className:"mb-6 p-3 bg-blue-50 border border-blue-200 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.Z,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:["Your wishlist is saved locally on this device.",(0,a.jsx)(i.default,{href:"/sign-up",className:"ml-1 font-medium underline hover:no-underline",children:"Create an account"})," to access it from anywhere."]})]})}),!y&&k&&t.length>0&&(0,a.jsx)(f.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-8 p-4 border border-[#e5e2d9] bg-[#f8f8f5] rounded-md",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(c.Z,{className:"h-5 w-5 text-[#8a8778] mt-1 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-serif font-medium text-[#2c2c27]",children:"Sync your wishlist across devices"}),(0,a.jsx)("p",{className:"text-sm text-[#5c5c52] mt-1",children:"Your wishlist works without an account and is saved locally. Sign in to sync it across all your devices."})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.default,{href:"/sign-up",className:"text-sm text-[#2c2c27] font-medium hover:text-[#8a8778] transition-colors",children:"Sign Up"}),(0,a.jsx)("button",{onClick:()=>{A(!1),sessionStorage.setItem("wishlist_prompt_dismissed","true")},className:"text-[#8a8778] hover:text-[#2c2c27] transition-colors","aria-label":"Dismiss",children:(0,a.jsx)(n.Z,{className:"h-4 w-4"})})]})]})}),0===t.length?(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)("div",{className:"inline-flex justify-center items-center w-16 h-16 bg-gray-100 rounded-full mb-4",children:(0,a.jsx)(c.Z,{className:"h-8 w-8 text-gray-400"})}),(0,a.jsx)("h2",{className:"text-xl font-medium mb-2",children:"Your wishlist is empty"}),(0,a.jsx)("p",{className:"text-gray-500 mb-2",children:"Add items you love to your wishlist. Review them anytime and easily move them to the cart."}),!y&&(0,a.jsx)("p",{className:"text-sm text-gray-400 mb-6",children:"No account needed - your wishlist is saved locally on this device."}),(0,a.jsx)(i.default,{href:"/categories",children:(0,a.jsx)(v.z,{children:"Continue Shopping"})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>(0,a.jsxs)("div",{className:"border rounded-md overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.default,{href:"/product/".concat(e.handle||"#"),children:(0,a.jsx)("div",{className:"aspect-square relative bg-gray-100",children:(0,a.jsx)(l.default,{src:e.image||"/placeholder-image.jpg",alt:e.name||"Product image",fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover"})})}),(0,a.jsx)("button",{onClick:()=>s(e.id),className:"absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-100","aria-label":"Remove from wishlist",children:(0,a.jsx)(d,{className:"h-4 w-4 text-gray-600"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)(i.default,{href:"/product/".concat(e.handle||"#"),children:(0,a.jsx)("h2",{className:"font-medium text-lg hover:underline",children:e.name||"Unnamed Product"})}),(0,a.jsxs)("p",{className:"text-gray-700 my-2",children:["₹",e.price?g(e.price):"0.00"]}),(0,a.jsxs)(v.z,{onClick:()=>F(e),className:"w-full mt-2 flex items-center justify-center gap-2",children:[(0,a.jsx)(o.Z,{className:"h-4 w-4"}),"Add to Cart"]})]})]},e.id))}),(0,a.jsx)("div",{className:"mt-12 text-center",children:(0,a.jsx)(i.default,{href:"/categories",children:(0,a.jsx)(v.z,{variant:"outline",children:"Continue Shopping"})})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full border-collapse",children:[(0,a.jsx)("thead",{className:"border-b border-[#e5e2d9]",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"py-4 text-left font-serif text-[#2c2c27]",children:"Product"}),(0,a.jsx)("th",{className:"py-4 text-left font-serif text-[#2c2c27]",children:"Price"}),(0,a.jsx)("th",{className:"py-4 text-center font-serif text-[#2c2c27]",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-[#e5e2d9]",children:t.map(e=>(0,a.jsxs)("tr",{className:"group",children:[(0,a.jsx)("td",{className:"py-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"relative mr-4 h-24 w-20 overflow-hidden bg-[#f4f3f0]",children:(0,a.jsx)(i.default,{href:"/product/".concat(e.handle||"#"),children:(0,a.jsx)(l.default,{src:e.image||"/placeholder-image.jpg",alt:e.name||"Product image",fill:!0,sizes:"(max-width: 768px) 80px, 120px",className:"object-cover object-center transition-transform duration-500 group-hover:scale-105"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(i.default,{href:"/product/".concat(e.handle||"#"),className:"font-serif text-lg text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:e.name||"Unnamed Product"}),(0,a.jsx)("p",{className:"text-sm text-[#8a8778]",children:e.material||"Material not specified"})]})]})}),(0,a.jsxs)("td",{className:"py-6 font-medium text-[#2c2c27]",children:["₹",e.price?g(e.price):"0.00"]}),(0,a.jsx)("td",{className:"py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsx)(f.E.button,{onClick:()=>F(e),className:"".concat(C[e.id]?"bg-[#2c2c27] text-[#f4f3f0]":"text-[#2c2c27]"," p-2 rounded-full transition-colors hover:text-[#8a8778]"),"aria-label":"Add to cart",whileTap:{scale:.95},children:C[e.id]?(0,a.jsx)(m.Z,{className:"h-5 w-5"}):(0,a.jsx)(o.Z,{className:"h-5 w-5"})}),(0,a.jsx)(f.E.button,{onClick:()=>s(e.id),className:"text-[#2c2c27] p-2 rounded-full hover:text-[#8a8778] transition-colors","aria-label":"Remove from wishlist",whileTap:{scale:.95},children:(0,a.jsx)(n.Z,{className:"h-5 w-5"})})]})})]},e.id))})]})})]})]}),(0,a.jsx)(p.x7,{position:"top-center",toastOptions:{duration:3e3,style:{background:"#F8F8F5",color:"#2C2C27",border:"1px solid #E5E2D9"},success:{iconTheme:{primary:"#2C2C27",secondary:"#F8F8F5"}},error:{iconTheme:{primary:"#2C2C27",secondary:"#F8F8F5"}}}})]})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,5717,8496,9429,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,6518,4595,3280,8790,1104,7158,6758,7044,5302,6628,5363,4754,1744],function(){return e(e.s=49759)}),_N_E=e.O()}]);