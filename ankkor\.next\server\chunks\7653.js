"use strict";exports.id=7653,exports.ids=[7653],exports.modules={67653:(e,r,t)=>{t.d(r,{verifySignature:()=>i});var n=t(70926);function i(e,r){let t=r?.currentSigningKey??process.env.QSTASH_CURRENT_SIGNING_KEY;if(!t)throw Error("currentSigningKey is required, either in the config or as env variable QSTASH_CURRENT_SIGNING_KEY");let i=r?.nextSigningKey??process.env.QSTASH_NEXT_SIGNING_KEY;if(!i)throw Error("nextSigningKey is required, either in the config or as env variable QSTASH_NEXT_SIGNING_KEY");let s=new n.nk({currentSigningKey:t,nextSigningKey:i});return async(t,n)=>{let i=t.headers["upstash-signature"];if(!i){n.status(400),n.send("`Upstash-Signature` header is missing"),n.end();return}if("string"!=typeof i)throw TypeError("`Upstash-Signature` header is not a string");let a=[];for await(let e of t)a.push("string"==typeof e?Buffer.from(e):e);let o=Buffer.concat(a).toString("utf8");if(!await s.verify({signature:i,body:o,clockTolerance:r?.clockTolerance})){n.status(400),n.send("Invalid signature"),n.end();return}try{t.body="application/json"===t.headers["content-type"]?JSON.parse(o):o}catch{t.body=o}return e(t,n)}}}};