{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "9W535lQKmkZccujMB0Bo2", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "iarBeOYcPRzXUy5OzQ0LF47G6UM+xkIqbgFniO5ZMyc=", "__NEXT_PREVIEW_MODE_ID": "9bdfbb3241261c91650f35018be9f09a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fa183797a794360028bd6e1bb379f52e2a891fd42f965f769b601f49ee9a8cd3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6b7866aa65b57403b3efb52183fdf037900e2834c5ffc6443e10ccba14595f49"}}}, "functions": {}, "sortedMiddleware": ["/"]}